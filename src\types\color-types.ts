export interface ThemeColors {
    /**
     * Default Non Themed Colors
     */

    /**
     * #fff
     */
    white: string;

    /**
     * #000
     */
    black: string;

    /**
     * "transparent"
     */
    transparent: string;

    /**
     * Extra Colors 
     */

    /**
     * * Light - #F1F5FE
     * * Dark - 
     */ 
    lightBlue : string;

    /**
     * * Light - #ECE2F2
     * * Dark - 
     */ 
    purpleLight : string;

    /**
     * * Light - #FFDCC4
     * * Dark - 
     */ 
    orange : string;

    /**
     * * Light - 
     * * Dark - 
    */
    foreground: string;

    /**
     * * Light - 
     * * Dark - 
    */
    background: string;

    /**
     * * Light - 
     * * Dark - 
    */
    text: string;

    /**
     * * Light - #EAE9F1 
     * * Dark - 
    */
    purple100: string;

    /**
     * * Light - #D2CFE2
     * * Dark - 
     */
    purple200: string;

    /**
     * * Light - #BDB9D4
     * * Dark - 
     */
    purple300: string;

    /**
     * * Light - #A5A0C5
     * * Dark - 
     */
    purple400: string;

    /**
     * * Light - #8F89B7
     * * Dark - 
     */
    purple500: string;

    /**
     * * Light - #69619E
     * * Dark - 
     */
    purple600: string;

    /**
     * * Light - #504978
     * * Dark - 
     */
    purple700: string;

    /**
     * * Light - #34304F
     * * Dark - 
     */
    purple800: string;

    /**
     * * Light - #1B1929 
     * * Dark - 
     */
    purple900: string;

    /**
     * * Light - #F4F4F4 
     * * Dark - 
     */
    neutral00: string;

    /**
     * * Light - #E9E9E9
     * * Dark - 
     */
    neutral10: string;

    /**
     * * Light - #D2D2D2
     * * Dark - 
     */
    neutral20: string;


    /**
     * * Light - #BCBCBC
     * * Dark - 
     */
    neutral30: string;

    /**
     * * Light - #A5A5A6
     * * Dark - 
     */
    neutral40: string;

    /**
     * * Light - #8F8F8F
     * * Dark - 
     */
    neutral50: string;

    /**
     * * Light - #797979
     * * Dark - 
     */
    neutral60: string;

    /**
     * * Light - #626263
     * * Dark - 
     */
    neutral70: string;

    /**
     * * Light - #4C4C4D
     * * Dark - 
     */
    neutral80: string;

    /**
     * * Light - #353536
     * * Dark - 
     */
    neutral90: string;

    /**
     * * Light - #1F1F20
     * * Dark - 
     */
    neutral100: string;

    /**
     * * Light - #FBF3F3
     * * Dark - 
     */
    negative00: string;

    /**
     * * Light - #F7E7E6
     * * Dark - 
     */
    negative10: string;

    /**
     * * Light - #E7B5B0
     * * Dark - 
     */
    negative20: string;

    /**
     * * Light - #DC918A
     * * Dark - 
     */
    negative30: string;

    /**
     * * Light - #CB5F54
     * * Dark - 
     */
    negative40: string;

    /**
     * * Light - #C14033
     * * Dark - 
     */
    negative50: string;

    /**
     * * Light - #B21000
     * * Dark - 
     */
    negative60: string;

    /**
     * * Light - #A20F00
     * * Dark - 
     */
    negative70: string;

    /**
     * * Light - #7E0B00
     * * Dark - 
     */
    negative80: string;

    /**
     * * Light - #620900
     * * Dark - 
     */
    negative90: string;

    /**
     * * Light - #4B0700
     * * Dark - 
     */
    negative100: string;

    /**
     * * Light - #FDFBF3
     * * Dark - 
     */
    warning00: string;

    /**
     * * Light - #FCF6E6
     * * Dark - 
     */
    warning10: string;
    
    /**
     * * Light - #F6E2B0
     * * Dark - 
     */
    warning20: string;

    /**
     * * Light - #F2D38A
     * * Dark - 
     */
    warning30: string;

    /**
     * * Light - #ECBF54
     * * Dark - 
     */
    warning40: string;

    /**
     * * Light - #E9B333
     * * Dark - 
     */
    warning50: string;

    /**
     * * Light - #E3A000
     * * Dark - 
     */
    warning60: string;

    /**
     * * Light - #CF9200
     * * Dark - 
     */
    warning70: string;
    
    /**
     * * Light - #A17200
     * * Dark - 
     */
    warning80: string;

    /**
     * * Light - #7D5800
     * * Dark - 
     */
    warning90: string;

    /**
     * * Light - #5F4300
     * * Dark - 
    */
    warning100: string;

    /**
     * * Light -#f3fbf7
     * * Dark - 
    */
    positive00: string;

    /**
     * * Light - #E6F7EE
     * * Dark - 
    */
    positive10: string;

    /**
     * * Light - #B0E5C9
     * * Dark - 
    */
    positive20: string;

    /**
     * * Light - #8AD8AF
     * * Dark - 
    */
    positive30: string;

    /**
     * * Light - #54C68A
     * * Dark - 
    */
    positive40: string;

    /**
     * * Light - #33BB73
     * * Dark - 
    */
    positive50: string;

    /**
     * * Light - #00AA50
     * * Dark - 
    */
    positive60: string;

    /**
     * * Light - #009B49
     * * Dark - 
    */
    positive70: string;

    /**
     * * Light - #007939
     * * Dark - 
    */
    positive80: string;

    /**
     * * Light - #005E2C
     * * Dark - 
    */
    positive90: string;

    /**
     * * Light - #004722
     * * Dark - 
    */
    positive100: string;

}

export type ThemeColorKeys = keyof ThemeColors;

