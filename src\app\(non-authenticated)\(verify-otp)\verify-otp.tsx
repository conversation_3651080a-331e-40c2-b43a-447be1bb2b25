import { Button, OtpInput, Text, View } from '@components/native'
import { StackBackButton } from "@components/shared"
import { Ionicons } from '@expo/vector-icons'
import CONST from "expo-constants"
import { router } from "expo-router"
import { useGlobalSearchParams } from 'expo-router/build/hooks'
import React, { useState } from 'react'
import { Keyboard, TouchableWithoutFeedback, useWindowDimensions } from "react-native"

const VerifyOtp = () => {
    const params = useGlobalSearchParams<{ email?: string }>();
    const [email, setEmail] = useState("");

    const { width } = useWindowDimensions();
    return (
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View flex={1} p={20} >
                {router.canGoBack() && (
                    <View pos="absolute" top={CONST.statusBarHeight + 10} left={15}>
                        <StackBackButton title="Enter authentication code" />
                    </View>
                )}
                <View
                    mt={CONST.statusBarHeight + 50}
                    display="flex"
                    fd="column"
                    w="100%"
                >
                    <View display="flex" fd="column">
                        <Text fw="500" color="neutral80">
                            A 4-digit code was sent to
                        </Text>
                        <Text
                            fw="400"
                            color="purple700"
                            style={{
                                flexWrap: 'wrap',
                                width: '100%',
                            }}
                        >
                            {params.email}
                        </Text>
                    </View>

                    <Text
                        onPress={router.back}
                        fw="400"
                        color="purple700"
                        tdl='underline'
                        style={{
                            alignSelf: 'flex-end',
                        }}
                    >
                        Change email
                    </Text>
                </View>
                <View mt={40}>
                    <OtpInput />
                    <Text onPress={()=>{}} ta='center' fw='600' color='neutral80' my={14}>Resend Code</Text>
                    <Button onPress={() => { }} mt={50} rightIcon={<Ionicons name='arrow-forward' color="white" size={20} />}>
                        Continue
                    </Button>
                </View>
            </View>
        </TouchableWithoutFeedback>
    )
}

export default VerifyOtp