import {Text, View} from '@components/native';
import {Assets} from '@constants';
import {useTheme} from '@context';
import {useLocalSearchParams} from 'expo-router';
import React from 'react';
import {Image, ScrollView} from 'react-native';

const ArticleScreen = () => {
    const params = useLocalSearchParams();
    const title = params.title;
    const image = params.image;
    const content = params.content;
    const {colors} = useTheme();

    return (
        <ScrollView style={{flex: 1, backgroundColor: colors.background}}>
            <Image
                source={image || Assets.placeholder.article}
                style={{width: '100%', height: 220, borderBottomLeftRadius: 16, borderBottomRightRadius: 16}}
                resizeMode="cover"
            />
            <View p={20}>
                <Text
                    fs="22"
                    fw="600"
                    color="neutral80"
                    mb={10}
                >
                    {title}
                </Text>
                <Text
                    fs="16"
                    fw="400"
                    color="neutral80"
                    style={{lineHeight: 24}}
                >
                    {content}
                </Text>
            </View>
        </ScrollView>
    );
};

export default ArticleScreen;
