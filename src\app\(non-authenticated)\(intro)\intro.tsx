import AppLogo from '@assets/svgs/app-logo.svg';
import { Button, Text, View } from '@components/native';
import { Assets } from '@constants';
import CONST from "expo-constants";
import { useRouter } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import { FlatList, Image, NativeScrollEvent, NativeSyntheticEvent, useWindowDimensions, ViewStyle } from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialCommunityIcons';

const IntroScreen = () => {
  const { height, width } = useWindowDimensions();
  const [activeIndex, setActiveIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);
  const { push } = useRouter();

  const carouselItem = [
    { image: Assets.images.intro1, text1: 'Feeling', text2: 'Heartbroken ?' },
    { image: Assets.images.intro2, text1: 'Facing Mental', text2: 'Breakdown?' },
    { image: Assets.images.intro3, text1: 'Loss of a furry', text2: 'friend?' },
  ];

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    const index = Math.round(offsetX / width);
    setActiveIndex(index);
  };

  const renderItem = ({ item, index }: { item: any, index: number }) => {
    const isActive = activeIndex === index;

    return (
      <View flex={1} display="flex" fd="column">
        <View
          flexCenterColumn
          gap={20}
          disableSizeMatter
          w={width}
          pos='absolute'
          z={1}
          top={20 + CONST.statusBarHeight}
        >
          <AppLogo />
          <View
            style={{
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column',
              opacity: isActive ? 1 : 0,
              width: '100%',
            }}
          >
            <Text fw="500" fs="26" color='text'>
              {item.text1}
            </Text>
            <Text fw="500" fs="26" color='text'>
              {item.text2}
            </Text>
          </View>
        </View>
        <View style={{ flex: 1, width, height: height - 150, overflow: 'hidden' }}>
          <Image
            source={item.image}
            style={{
              width,
              height: '100%',
              position: 'absolute',
              transform: [{ translateX: isActive ? 0 : (index < activeIndex ? -width : width) }],
            }}
            resizeMode="cover"
          />
        </View>
      </View>
    );
  };

  const renderDot = (index: number) => {
    const isActive = activeIndex === index;
    const style: ViewStyle = {
      ...styles.dot,
      backgroundColor: '#FFFFFF',
      opacity: isActive ? 1 : 0.3,
    };

    return <View key={index} style={style} />;
  };

  useEffect(() => {
    const interval = setInterval(() => {
      const nextIndex = (activeIndex + 1) % carouselItem.length;
      flatListRef.current?.scrollToIndex({ index: nextIndex, animated: true });
      setActiveIndex(nextIndex);
    }, 5000);
    return () => clearInterval(interval);
  }, [activeIndex]);

  return (
    <View flex={1} display="flex" fd="column">
      <FlatList
        ref={flatListRef}
        data={carouselItem}
        renderItem={renderItem}
        keyExtractor={(_, index) => index.toString()}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        scrollToOverflowEnabled={false}
        bounces={false}
      />
      <View pos="absolute" bottom={60} w={width} disableSizeMatter flexCenterColumn gap={20}>
        <View display="flex" fd="row" gap={8}>
          {carouselItem.map((_, index) => renderDot(index))}
        </View>
        <Button
          bg="white"
          color="black"
          px={20}
          rightIcon={<MaterialIcons size={20} name="arrow-right" />}
          isFullWidth={false}
          onPress={() => push("/pre-auth")}
        >
          Join us Now
        </Button>
      </View>
    </View>
  );
};

const styles = {
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  } as ViewStyle,
};

export default IntroScreen;
