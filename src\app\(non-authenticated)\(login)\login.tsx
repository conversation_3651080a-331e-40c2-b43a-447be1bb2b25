import AppLogo from "@assets/svgs/app-logo.svg"
import AppleIcon from "@assets/svgs/apple-icon.svg"
import GoogleIcon from "@assets/svgs/google-icon.svg"
import { Button, Text, TextInput, View } from '@components/native'
import { StackBackButton } from "@components/shared"
import { BounceTap } from "@components/shared/animated"
import CONST from "expo-constants"
import { router } from "expo-router"
import React, { useState } from 'react'
import { Keyboard, TouchableWithoutFeedback } from "react-native"

const LoginScreen = () => {
    const [email, setEmail] = useState("");
    return (
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View flex={1} p={20} display="flex" jc="space-evenly">
                {router.canGoBack() && (
                    <View pos="absolute" top={CONST.statusBarHeight + 10} left={15}>
                        <StackBackButton />
                    </View>
                )}
                <View flexCenterColumn mt={CONST.statusBarHeight} gap={20}>
                    <AppLogo width={80} height={90} />
                    <Text fw="500" color="neutral80" ff="PlayfairMedium" fs="26">Welcome Back !</Text>
                </View>
                <View>
                    <TextInput label='Email address' keyboardType='email-address' value={email} onSuffixIconPress={() => setEmail('')} onChangeText={setEmail} />
                    <Button mt={30} onPress={() => router.push("/verify-otp?email=<EMAIL>")}>LOG IN</Button>
                </View>
                <View>
                    <View flexCenterColumn gap={20}>
                        <View disableSizeMatter bg="neutral30" w={"100%"} h={1} />
                        <Text fw="500" color="neutral80" fs="12">Or continue with</Text>
                        <View flexCenterRow gap={14}>
                            <BounceTap onPress={() => { }}>
                                <View br={100} h={45} flexCenterRow disableSizeMatter w={45} bg="purpleLight">
                                    <GoogleIcon />
                                </View>
                            </BounceTap>
                            <BounceTap onPress={() => { }}>
                                <View br={100} h={45} flexCenterRow disableSizeMatter w={45} bg="purpleLight">
                                    <AppleIcon />
                                </View>
                            </BounceTap>
                        </View>
                        <Text fs="12">NOT A MEMBER ?
                            <Text onPress={() => router.replace("/signup")} fs="12" style={{ color: "#8E97FD" }}> REGISTER NOW</Text>
                        </Text>
                    </View>
                </View>
            </View>
        </TouchableWithoutFeedback>
    )
}

export default LoginScreen