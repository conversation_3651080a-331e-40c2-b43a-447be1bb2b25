
import { useTheme } from "@context/Theme/ThemeContext";
import { FF, FS, FW, ThemeColorKeys } from "@types";
import { forwardRef } from "react";
import { Text as RNText, TextProps as RNTextProps, TextStyle } from "react-native";
import { ms } from "react-native-size-matters";

export interface TextProps extends RNTextProps {
    /**
     * Font Size
     */
    fs?: FS;
    /**
     * Font Family
     */
    ff?: FF;
    /**
     * Color enums eg "Foreground" , "Background" , etc..
     */
    color?: ThemeColorKeys; // Use keyof ThemeColors for string keys

    /**
     * Font Weight
     */
    fw?: FW;
    /**
     * Text Align
     */
    ta?: "center" | "justify" | "left" | "right" | "auto";

    /**
     * Margin Left
     */
    ml?: number;
    /**
     * Margin Right
     */
    mr?: number;
    /**
     * Margin Top
     */
    mt?: number;
    /**
     * Margin Bottom
     */
    mb?: number;
    /**
     * Margin Horizontal
     */
    mx?: number;
    /**
     * Margin Vertical
     */
    my?: number;

    tdl?: "none" | "underline" | "line-through" | "underline line-through" | undefined;
}


export const FontFamilyMap = {
    "300": "MontserratLight",
    "400": "MontserratRegular",
    "500": "MontserratMedium",
    "600": "MontserratSemiBold",
    "700": "MontserratBold",
} satisfies Record<FW, FF>;

export const Text = forwardRef<RNText, TextProps>(({
    fw = "400",
    fs = "16",
    color = "text",
    style,
    ff,
    ta,
    ml,
    mr,
    mt,
    mb,
    mx,
    tdl,
    my,
    ...props
}, ref) => {
    const { colors } = useTheme();

    const defaultStyles = {
        fontSize: ms(Number(fs), 0.5),
        color: colors[color],
        fontFamily: ff ?? FontFamilyMap[fw],
        fontWeight: fw,
        textAlign: ta,
        marginLeft: ml,
        marginRight: mr,
        marginTop: mt,
        marginBottom: mb,
        marginHorizontal: mx,
        marginVertical: my,
        textDecorationLine: tdl,
    } as TextStyle;

    return (
        <RNText
            ref={ref}
            style={[defaultStyles, style]}
            {...props}
        >
            {props.children}
        </RNText>
    );
});

Text.displayName = "Text";