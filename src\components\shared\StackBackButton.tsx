import { Text, View } from "@components/native"
import { useTheme } from "@context"
import { Ionicons } from "@expo/vector-icons"
import { router } from "expo-router"
import { BounceTap } from "./animated"



export const StackBackButton = ({ title }: { title?: string }) => {
    const { colors } = useTheme();

    return (
        <View {...title && { display: "flex", fd: "row", ai: "center" }} gap={0}>
            <BounceTap onPress={() => router.back()}>
                <View h={50} bg="transparent" bc="purpleLight" bw={1} flexCenterRow style={{ height: 50, width: 50 }} br={100} >
                    <Ionicons name="arrow-back" color={colors.neutral70} size={22} />
                </View>
            </BounceTap>
            {title && <Text ml={15} fw="600" color="neutral80" ta="center" fs="18">{title}</Text>}
        </View>
    )
}

