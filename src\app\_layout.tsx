import {AuthProvider} from '@context/Auth/AuthContext';
import {ThemeProvider, useTheme} from '@context/Theme/ThemeContext';
import {QueryClient, QueryClientProvider} from '@tanstack/react-query';
import {useFonts} from 'expo-font';
import {Stack} from 'expo-router';
import {StatusBar} from 'expo-status-bar';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import 'react-native-reanimated';

const queryClient = new QueryClient();

export default function RootLayout() {
    const [loaded] = useFonts({
        MontserratLight: require('../assets/fonts/Montserrat-Light.ttf'),
        MontserratRegular: require('../assets/fonts/Montserrat-Regular.ttf'),
        MontserratMedium: require('../assets/fonts/Montserrat-Medium.ttf'),
        MontserratSemiBold: require('../assets/fonts/Montserrat-SemiBold.ttf'),
        MontserratBold: require('../assets/fonts/Montserrat-Bold.ttf'),
        PlayfairRegular: require('@assets/fonts/PlayfairDisplay-Regular.ttf'),
        PlayfairMedium: require('@assets/fonts/PlayfairDisplay-Medium.ttf'),
        PlayfairSemiBold: require('@assets/fonts/PlayfairDisplay-SemiBold.ttf'),
        PlayfairBold: require('@assets/fonts/PlayfairDisplay-Bold.ttf'),
        PoppinsRegular: require('@assets/fonts/Poppins-Regular.ttf'),
    });

    if (!loaded) {
        // Async font loading only occurs in development.
        return null;
    }

    return (
        <QueryClientProvider client={queryClient}>
            <GestureHandlerRootView style={{flex: 1}}>
                <ThemeProvider>
                    <AuthProvider>
                        <Stack>
                            <Stack.Screen
                                name="(authenticated)"
                                options={{headerShown: false}}
                            />
                            <Stack.Screen
                                name="(non-authenticated)"
                                options={{headerShown: false}}
                            />
                            <Stack.Screen name="+not-found" />
                        </Stack>
                    </AuthProvider>
                    <DefaultStatusBar />
                </ThemeProvider>
            </GestureHandlerRootView>
        </QueryClientProvider>
    );
}

const DefaultStatusBar = () => {
    const {theme} = useTheme();
    return <StatusBar style={theme == 'dark' ? 'light' : 'dark'} />;
};
