// packages/useStitch/apis/profile-apis.ts
import { ApiResponse, MutationApiConfig, PaginatedApiConfig, PaginatedResponse, QueryApiConfig } from '../types/common-api-types';
import { ExampleAutoLoginUserResponse, ExampleProduct, ExampleUpdateUserMutationData, ExampleUpdateUserMutationResponse } from './example-types';




export namespace ExampleApis {
  export const exampleAutoLogin = "exampleAutoLogin" as const;
  export const exampleUpdateUserDetails = 'exampleUpdateUserDetails' as const;
  export const getUser = 'getUser' as const;
  export const getPaginatedProducts = 'getPaginatedProducts' as const;
}

export const exampleApiConfig = {
  getUser: {
    path: '/testing',
    method: 'GET',
    protected: false,
    mutationBody: undefined as any,
    responseType: undefined as unknown as ApiResponse<ExampleAutoLoginUserResponse>,
    baseCacheKey: 'example_auto_login',
    staleTime: 2 * 60 * 1000,
    type: 'mutation' as const,
  } satisfies MutationApiConfig<ExampleAutoLoginUserResponse, any>,
  // This is a query endpoint that fetches user data wihtout pagination
  exampleAutoLogin: {
    path: '/auto-login',
    method: 'GET',
    protected: true,
    responseType: undefined as unknown as ApiResponse<ExampleAutoLoginUserResponse>,
    baseCacheKey: 'auto_login_user',
    staleTime: 2 * 60 * 1000,
    type: 'query' as const,
  } satisfies QueryApiConfig<ExampleAutoLoginUserResponse>,
  // This is a mutation endpoint that updates user details
  exampleUpdateUserDetails: {
    path: '/update-user',
    method: 'POST',
    protected: false,
    mutationBody: undefined as unknown as ExampleUpdateUserMutationData,
    responseType: undefined as unknown as ApiResponse<ExampleUpdateUserMutationResponse>,
    baseCacheKey: 'update_user_details',
    staleTime: 0,
    type: 'mutation' as const,
  } satisfies MutationApiConfig<ExampleUpdateUserMutationResponse, ExampleUpdateUserMutationData>,
  getPaginatedProducts: {
    path: '/products',
    method: 'GET',
    protected: true,
    isPaginated: true as const,
    pageSize: 10,
    responseType: undefined as unknown as PaginatedResponse<ExampleProduct[]>,
    baseCacheKey: 'products_paginated',
    staleTime: 2 * 60 * 1000,
    // queryParamBuilder: (params: { filter?: string }) => ({
    //   filter: params.filter || '',
    // }),
    type: 'paginated' as const,
  } satisfies PaginatedApiConfig<ExampleProduct[]>,
};
