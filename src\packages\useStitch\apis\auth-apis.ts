// packages/useStitch/apis/profile-apis.ts
import { SignupMutationBody } from "@packages/useStitch/types";
import { ApiResponse, MutationApiConfig } from '../types/common-api-types';

export namespace AuthApis {
  export const signup = "signup" as const;
}

export const authApiConfig = {
    signup: {
    path: '/auth/signup',
    method: 'POST',
    protected: false,
    mutationBody: undefined as unknown as  SignupMutationBody,
    responseType: undefined as unknown as ApiResponse<any>,
    baseCacheKey: 'example_auto_login',
    staleTime: 0,
    type: 'mutation' as const,
  } satisfies MutationApiConfig<any, SignupMutationBody>,
};
