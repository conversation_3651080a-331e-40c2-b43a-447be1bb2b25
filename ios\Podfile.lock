PODS:
  - boost (1.84.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - DoubleConversion (1.1.6)
  - EXConstants (17.1.6):
    - ExpoModulesCore
  - Expo (53.0.9):
    - DoubleConversion
    - ExpoModulesCore
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactAppDependencyProvider
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - ExpoAsset (11.1.5):
    - ExpoModulesCore
  - ExpoBlur (14.1.4):
    - ExpoModulesCore
  - ExpoFileSystem (18.1.10):
    - ExpoModulesCore
  - ExpoFont (13.3.1):
    - ExpoModulesCore
  - ExpoHaptics (14.1.4):
    - ExpoModulesCore
  - ExpoHead (5.0.7):
    - ExpoModulesCore
  - ExpoImage (2.1.7):
    - ExpoModulesCore
    - libavif/libdav1d
    - SDWebImage (~> 5.21.0)
    - SDWebImageAVIFCoder (~> 0.11.0)
    - SDWebImageSVGCoder (~> 1.7.0)
    - SDWebImageWebPCoder (~> 0.14.6)
  - ExpoKeepAwake (14.1.4):
    - ExpoModulesCore
  - ExpoLinking (7.1.5):
    - ExpoModulesCore
  - ExpoModulesCore (2.3.13):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - ExpoSecureStore (14.2.3):
    - ExpoModulesCore
  - ExpoSplashScreen (0.30.8):
    - ExpoModulesCore
  - ExpoSymbols (0.4.4):
    - ExpoModulesCore
  - ExpoSystemUI (5.0.7):
    - ExpoModulesCore
  - ExpoWebBrowser (14.1.6):
    - ExpoModulesCore
  - fast_float (6.1.4)
  - FBLazyVector (0.79.2)
  - fmt (11.0.2)
  - glog (0.3.5)
  - hermes-engine (0.79.2):
    - hermes-engine/Pre-built (= 0.79.2)
  - hermes-engine/Pre-built (0.79.2)
  - libavif/core (0.11.1)
  - libavif/libdav1d (0.11.1):
    - libavif/core
    - libdav1d (>= 0.6.0)
  - libdav1d (1.2.0)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - RCT-Folly (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Default (= 2024.11.18.00)
  - RCT-Folly/Default (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
  - RCT-Folly/Fabric (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
  - RCTDeprecation (0.79.2)
  - RCTRequired (0.79.2)
  - RCTTypeSafety (0.79.2):
    - FBLazyVector (= 0.79.2)
    - RCTRequired (= 0.79.2)
    - React-Core (= 0.79.2)
  - React (0.79.2):
    - React-Core (= 0.79.2)
    - React-Core/DevSupport (= 0.79.2)
    - React-Core/RCTWebSocket (= 0.79.2)
    - React-RCTActionSheet (= 0.79.2)
    - React-RCTAnimation (= 0.79.2)
    - React-RCTBlob (= 0.79.2)
    - React-RCTImage (= 0.79.2)
    - React-RCTLinking (= 0.79.2)
    - React-RCTNetwork (= 0.79.2)
    - React-RCTSettings (= 0.79.2)
    - React-RCTText (= 0.79.2)
    - React-RCTVibration (= 0.79.2)
  - React-callinvoker (0.79.2)
  - React-Core (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default (= 0.79.2)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/Default (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/DevSupport (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default (= 0.79.2)
    - React-Core/RCTWebSocket (= 0.79.2)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTWebSocket (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default (= 0.79.2)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-CoreModules (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - RCT-Folly (= 2024.11.18.00)
    - RCTTypeSafety (= 0.79.2)
    - React-Core/CoreModulesHeaders (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-jsinspector
    - React-jsinspectortracing
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTFBReactNativeSpec
    - React-RCTImage (= 0.79.2)
    - ReactCommon
    - SocketRocket (= 0.7.1)
  - React-cxxreact (0.79.2):
    - boost
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-callinvoker (= 0.79.2)
    - React-debug (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-jsinspector
    - React-jsinspectortracing
    - React-logger (= 0.79.2)
    - React-perflogger (= 0.79.2)
    - React-runtimeexecutor (= 0.79.2)
    - React-timing (= 0.79.2)
  - React-debug (0.79.2)
  - React-defaultsnativemodule (0.79.2):
    - hermes-engine
    - RCT-Folly
    - React-domnativemodule
    - React-featureflagsnativemodule
    - React-hermes
    - React-idlecallbacksnativemodule
    - React-jsi
    - React-jsiexecutor
    - React-microtasksnativemodule
    - React-RCTFBReactNativeSpec
  - React-domnativemodule (0.79.2):
    - hermes-engine
    - RCT-Folly
    - React-Fabric
    - React-FabricComponents
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.79.2)
    - React-Fabric/attributedstring (= 0.79.2)
    - React-Fabric/componentregistry (= 0.79.2)
    - React-Fabric/componentregistrynative (= 0.79.2)
    - React-Fabric/components (= 0.79.2)
    - React-Fabric/consistency (= 0.79.2)
    - React-Fabric/core (= 0.79.2)
    - React-Fabric/dom (= 0.79.2)
    - React-Fabric/imagemanager (= 0.79.2)
    - React-Fabric/leakchecker (= 0.79.2)
    - React-Fabric/mounting (= 0.79.2)
    - React-Fabric/observers (= 0.79.2)
    - React-Fabric/scheduler (= 0.79.2)
    - React-Fabric/telemetry (= 0.79.2)
    - React-Fabric/templateprocessor (= 0.79.2)
    - React-Fabric/uimanager (= 0.79.2)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/legacyviewmanagerinterop (= 0.79.2)
    - React-Fabric/components/root (= 0.79.2)
    - React-Fabric/components/scrollview (= 0.79.2)
    - React-Fabric/components/view (= 0.79.2)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/scrollview (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-renderercss
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/consistency (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/core (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/dom (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events (= 0.79.2)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers/events (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-performancetimeline
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager/consistency (= 0.79.2)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager/consistency (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricComponents (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components (= 0.79.2)
    - React-FabricComponents/textlayoutmanager (= 0.79.2)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components/inputaccessory (= 0.79.2)
    - React-FabricComponents/components/iostextinput (= 0.79.2)
    - React-FabricComponents/components/modal (= 0.79.2)
    - React-FabricComponents/components/rncore (= 0.79.2)
    - React-FabricComponents/components/safeareaview (= 0.79.2)
    - React-FabricComponents/components/scrollview (= 0.79.2)
    - React-FabricComponents/components/text (= 0.79.2)
    - React-FabricComponents/components/textinput (= 0.79.2)
    - React-FabricComponents/components/unimplementedview (= 0.79.2)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/inputaccessory (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/iostextinput (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/modal (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/rncore (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/safeareaview (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/scrollview (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/text (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/textinput (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/unimplementedview (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/textlayoutmanager (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricImage (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired (= 0.79.2)
    - RCTTypeSafety (= 0.79.2)
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.79.2)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-featureflags (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
  - React-featureflagsnativemodule (0.79.2):
    - hermes-engine
    - RCT-Folly
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
  - React-graphics (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-utils
  - React-hermes (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-cxxreact (= 0.79.2)
    - React-jsi
    - React-jsiexecutor (= 0.79.2)
    - React-jsinspector
    - React-jsinspectortracing
    - React-perflogger (= 0.79.2)
    - React-runtimeexecutor
  - React-idlecallbacksnativemodule (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-ImageManager (0.79.2):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - ReactCommon/turbomodule/bridging
  - React-jsi (0.79.2):
    - boost
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
  - React-jsiexecutor (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-cxxreact (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-jsinspector
    - React-jsinspectortracing
    - React-perflogger (= 0.79.2)
  - React-jsinspector (0.79.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - React-featureflags
    - React-jsi
    - React-jsinspectortracing
    - React-perflogger (= 0.79.2)
    - React-runtimeexecutor (= 0.79.2)
  - React-jsinspectortracing (0.79.2):
    - RCT-Folly
    - React-oscompat
  - React-jsitooling (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - React-cxxreact (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-jsinspector
    - React-jsinspectortracing
  - React-jsitracing (0.79.2):
    - React-jsi
  - React-logger (0.79.2):
    - glog
  - React-Mapbuffer (0.79.2):
    - glog
    - React-debug
  - React-microtasksnativemodule (0.79.2):
    - hermes-engine
    - RCT-Folly
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
  - react-native-safe-area-context (5.4.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - react-native-safe-area-context/common (= 5.4.0)
    - react-native-safe-area-context/fabric (= 5.4.0)
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-safe-area-context/common (5.4.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-safe-area-context/fabric (5.4.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - react-native-safe-area-context/common
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-webview (13.13.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-NativeModulesApple (0.79.2):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-oscompat (0.79.2)
  - React-perflogger (0.79.2):
    - DoubleConversion
    - RCT-Folly (= 2024.11.18.00)
  - React-performancetimeline (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
    - React-cxxreact
    - React-featureflags
    - React-jsinspectortracing
    - React-perflogger
    - React-timing
  - React-RCTActionSheet (0.79.2):
    - React-Core/RCTActionSheetHeaders (= 0.79.2)
  - React-RCTAnimation (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
    - RCTTypeSafety
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
  - React-RCTAppDelegate (0.79.2):
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-debug
    - React-defaultsnativemodule
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsitooling
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RCTImage
    - React-RCTNetwork
    - React-RCTRuntime
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-runtimescheduler
    - React-utils
    - ReactCommon
  - React-RCTBlob (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-jsinspectortracing
    - React-performancetimeline
    - React-RCTAnimation
    - React-RCTImage
    - React-RCTText
    - React-rendererconsistency
    - React-renderercss
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTFBReactNativeSpec (0.79.2):
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTImage (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
    - RCTTypeSafety
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.79.2):
    - React-Core/RCTLinkingHeaders (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.79.2)
  - React-RCTNetwork (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
    - RCTTypeSafety
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
  - React-RCTRuntime (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-Core
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsinspectortracing
    - React-jsitooling
    - React-RuntimeApple
    - React-RuntimeCore
    - React-RuntimeHermes
  - React-RCTSettings (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
    - RCTTypeSafety
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
  - React-RCTText (0.79.2):
    - React-Core/RCTTextHeaders (= 0.79.2)
    - Yoga
  - React-RCTVibration (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
  - React-rendererconsistency (0.79.2)
  - React-renderercss (0.79.2):
    - React-debug
    - React-utils
  - React-rendererdebug (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - RCT-Folly (= 2024.11.18.00)
    - React-debug
  - React-rncore (0.79.2)
  - React-RuntimeApple (0.79.2):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
  - React-RuntimeCore (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-cxxreact
    - React-Fabric
    - React-featureflags
    - React-hermes
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-performancetimeline
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
  - React-runtimeexecutor (0.79.2):
    - React-jsi (= 0.79.2)
  - React-RuntimeHermes (0.79.2):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsinspectortracing
    - React-jsitooling
    - React-jsitracing
    - React-RuntimeCore
    - React-utils
  - React-runtimescheduler (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspectortracing
    - React-performancetimeline
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-timing
    - React-utils
  - React-timing (0.79.2)
  - React-utils (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-debug
    - React-hermes
    - React-jsi (= 0.79.2)
  - ReactAppDependencyProvider (0.79.2):
    - ReactCodegen
  - ReactCodegen (0.79.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - ReactCommon (0.79.2):
    - ReactCommon/turbomodule (= 0.79.2)
  - ReactCommon/turbomodule (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-callinvoker (= 0.79.2)
    - React-cxxreact (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-logger (= 0.79.2)
    - React-perflogger (= 0.79.2)
    - ReactCommon/turbomodule/bridging (= 0.79.2)
    - ReactCommon/turbomodule/core (= 0.79.2)
  - ReactCommon/turbomodule/bridging (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-callinvoker (= 0.79.2)
    - React-cxxreact (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-logger (= 0.79.2)
    - React-perflogger (= 0.79.2)
  - ReactCommon/turbomodule/core (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-callinvoker (= 0.79.2)
    - React-cxxreact (= 0.79.2)
    - React-debug (= 0.79.2)
    - React-featureflags (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-logger (= 0.79.2)
    - React-perflogger (= 0.79.2)
    - React-utils (= 0.79.2)
  - RNFlashList (1.7.6):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNGestureHandler (2.24.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNReanimated (3.17.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated (= 3.17.5)
    - RNReanimated/worklets (= 3.17.5)
    - Yoga
  - RNReanimated/reanimated (3.17.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated/apple (= 3.17.5)
    - Yoga
  - RNReanimated/reanimated/apple (3.17.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNReanimated/worklets (3.17.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/worklets/apple (= 3.17.5)
    - Yoga
  - RNReanimated/worklets/apple (3.17.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (4.10.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNScreens/common (= 4.10.0)
    - Yoga
  - RNScreens/common (4.10.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNSVG (15.11.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNSVG/common (= 15.11.2)
    - Yoga
  - RNSVG/common (15.11.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNVectorIcons (10.2.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - SDWebImageAVIFCoder (0.11.0):
    - libavif/core (>= 0.11.0)
    - SDWebImage (~> 5.10)
  - SDWebImageSVGCoder (1.7.0):
    - SDWebImage/Core (~> 5.6)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - SocketRocket (0.7.1)
  - Yoga (0.0.0)

DEPENDENCIES:
  - "boost (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/third-party-podspecs/boost.podspec`)"
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - "DoubleConversion (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)"
  - "EXConstants (from `../node_modules/.pnpm/expo-constants@17.1.6_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0_co4hhjyzrdr3pp5adexxl2tlum/node_modules/expo-constants/ios`)"
  - "Expo (from `../node_modules/.pnpm/expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79.2_@babel+core@7.27_opbxkmvz24dt244cuwl2mldehe/node_modules/expo`)"
  - "ExpoAsset (from `../node_modules/.pnpm/expo-asset@11.1.5_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79._4arpie4yygfwnrbebxkoeilw6i/node_modules/expo-asset/ios`)"
  - "ExpoBlur (from `../node_modules/.pnpm/expo-blur@14.1.4_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79.2_flw2tp6g4yim4ewnunktr3gdd4/node_modules/expo-blur/ios`)"
  - "ExpoFileSystem (from `../node_modules/.pnpm/expo-file-system@18.1.10_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-nativ_7pnfoy7n3lumk6x33bkiz3liiy/node_modules/expo-file-system/ios`)"
  - "ExpoFont (from `../node_modules/.pnpm/expo-font@13.3.1_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79.2_bv2didvas4rkg6g4zzlpasirou/node_modules/expo-font/ios`)"
  - "ExpoHaptics (from `../node_modules/.pnpm/expo-haptics@14.1.4_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.7_coovupracgrmnypfwcoaafjgji/node_modules/expo-haptics/ios`)"
  - "ExpoHead (from `../node_modules/.pnpm/expo-router@5.0.7_4jnia56itigviazofy2ikjtvym/node_modules/expo-router/ios`)"
  - "ExpoImage (from `../node_modules/.pnpm/expo-image@2.1.7_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79.2_6ngynvfqyrde7r3tr6c67khpce/node_modules/expo-image/ios`)"
  - "ExpoKeepAwake (from `../node_modules/.pnpm/expo-keep-awake@14.1.4_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@_dvqn72nv4tnmxlkguzmsutlpsm/node_modules/expo-keep-awake/ios`)"
  - "ExpoLinking (from `../node_modules/.pnpm/expo-linking@7.1.5_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79_fgpdjjd25j2qavggaz6v6p5mfi/node_modules/expo-linking/ios`)"
  - "ExpoModulesCore (from `../node_modules/.pnpm/expo-modules-core@2.3.13/node_modules/expo-modules-core`)"
  - "ExpoSecureStore (from `../node_modules/.pnpm/expo-secure-store@14.2.3_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-nativ_b26lyp22cdohv2fyhpvbwyw2qq/node_modules/expo-secure-store/ios`)"
  - "ExpoSplashScreen (from `../node_modules/.pnpm/expo-splash-screen@0.30.8_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-nati_l4hgk35sh5ccwxwcvobjkqmq3a/node_modules/expo-splash-screen/ios`)"
  - "ExpoSymbols (from `../node_modules/.pnpm/expo-symbols@0.4.4_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79_my75exxaguaoj3lxhaupuz4ese/node_modules/expo-symbols/ios`)"
  - "ExpoSystemUI (from `../node_modules/.pnpm/expo-system-ui@5.0.7_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0._x2ru3hzuu4x3tcua4xmpfiebdq/node_modules/expo-system-ui/ios`)"
  - "ExpoWebBrowser (from `../node_modules/.pnpm/expo-web-browser@14.1.6_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native_te7yqvz2kotvxpxbhzfcx4v7sy/node_modules/expo-web-browser/ios`)"
  - "fast_float (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/third-party-podspecs/fast_float.podspec`)"
  - "FBLazyVector (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/FBLazyVector`)"
  - "fmt (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/third-party-podspecs/fmt.podspec`)"
  - "glog (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/third-party-podspecs/glog.podspec`)"
  - "hermes-engine (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)"
  - "RCT-Folly (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)"
  - "RCT-Folly/Fabric (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)"
  - "RCTDeprecation (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)"
  - "RCTRequired (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/Required`)"
  - "RCTTypeSafety (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/TypeSafety`)"
  - "React (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/`)"
  - "React-callinvoker (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/callinvoker`)"
  - "React-Core (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/`)"
  - "React-Core/RCTWebSocket (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/`)"
  - "React-CoreModules (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/React/CoreModules`)"
  - "React-cxxreact (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/cxxreact`)"
  - "React-debug (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/debug`)"
  - "React-defaultsnativemodule (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/nativemodule/defaults`)"
  - "React-domnativemodule (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/nativemodule/dom`)"
  - "React-Fabric (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon`)"
  - "React-FabricComponents (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon`)"
  - "React-FabricImage (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon`)"
  - "React-featureflags (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/featureflags`)"
  - "React-featureflagsnativemodule (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/nativemodule/featureflags`)"
  - "React-graphics (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/renderer/graphics`)"
  - "React-hermes (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/hermes`)"
  - "React-idlecallbacksnativemodule (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks`)"
  - "React-ImageManager (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)"
  - "React-jserrorhandler (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/jserrorhandler`)"
  - "React-jsi (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/jsi`)"
  - "React-jsiexecutor (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/jsiexecutor`)"
  - "React-jsinspector (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/jsinspector-modern`)"
  - "React-jsinspectortracing (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/jsinspector-modern/tracing`)"
  - "React-jsitooling (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/jsitooling`)"
  - "React-jsitracing (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/hermes/executor/`)"
  - "React-logger (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/logger`)"
  - "React-Mapbuffer (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon`)"
  - "React-microtasksnativemodule (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/nativemodule/microtasks`)"
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - "React-NativeModulesApple (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)"
  - "React-oscompat (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/oscompat`)"
  - "React-perflogger (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/reactperflogger`)"
  - "React-performancetimeline (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/performance/timeline`)"
  - "React-RCTActionSheet (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/ActionSheetIOS`)"
  - "React-RCTAnimation (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/NativeAnimation`)"
  - "React-RCTAppDelegate (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/AppDelegate`)"
  - "React-RCTBlob (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/Blob`)"
  - "React-RCTFabric (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/React`)"
  - "React-RCTFBReactNativeSpec (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/React`)"
  - "React-RCTImage (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/Image`)"
  - "React-RCTLinking (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/LinkingIOS`)"
  - "React-RCTNetwork (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/Network`)"
  - "React-RCTRuntime (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/React/Runtime`)"
  - "React-RCTSettings (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/Settings`)"
  - "React-RCTText (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/Text`)"
  - "React-RCTVibration (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/Vibration`)"
  - "React-rendererconsistency (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/renderer/consistency`)"
  - "React-renderercss (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/renderer/css`)"
  - "React-rendererdebug (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/renderer/debug`)"
  - "React-rncore (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon`)"
  - "React-RuntimeApple (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/runtime/platform/ios`)"
  - "React-RuntimeCore (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/runtime`)"
  - "React-runtimeexecutor (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/runtimeexecutor`)"
  - "React-RuntimeHermes (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/runtime`)"
  - "React-runtimescheduler (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)"
  - "React-timing (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/timing`)"
  - "React-utils (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/utils`)"
  - ReactAppDependencyProvider (from `build/generated/ios`)
  - ReactCodegen (from `build/generated/ios`)
  - "ReactCommon/turbomodule/core (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon`)"
  - "RNFlashList (from `../node_modules/@shopify/flash-list`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - "Yoga (from `../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/yoga`)"

SPEC REPOS:
  trunk:
    - libavif
    - libdav1d
    - libwebp
    - SDWebImage
    - SDWebImageAVIFCoder
    - SDWebImageSVGCoder
    - SDWebImageWebPCoder
    - SocketRocket

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  EXConstants:
    :path: "../node_modules/.pnpm/expo-constants@17.1.6_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0_co4hhjyzrdr3pp5adexxl2tlum/node_modules/expo-constants/ios"
  Expo:
    :path: "../node_modules/.pnpm/expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79.2_@babel+core@7.27_opbxkmvz24dt244cuwl2mldehe/node_modules/expo"
  ExpoAsset:
    :path: "../node_modules/.pnpm/expo-asset@11.1.5_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79._4arpie4yygfwnrbebxkoeilw6i/node_modules/expo-asset/ios"
  ExpoBlur:
    :path: "../node_modules/.pnpm/expo-blur@14.1.4_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79.2_flw2tp6g4yim4ewnunktr3gdd4/node_modules/expo-blur/ios"
  ExpoFileSystem:
    :path: "../node_modules/.pnpm/expo-file-system@18.1.10_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-nativ_7pnfoy7n3lumk6x33bkiz3liiy/node_modules/expo-file-system/ios"
  ExpoFont:
    :path: "../node_modules/.pnpm/expo-font@13.3.1_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79.2_bv2didvas4rkg6g4zzlpasirou/node_modules/expo-font/ios"
  ExpoHaptics:
    :path: "../node_modules/.pnpm/expo-haptics@14.1.4_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.7_coovupracgrmnypfwcoaafjgji/node_modules/expo-haptics/ios"
  ExpoHead:
    :path: "../node_modules/.pnpm/expo-router@5.0.7_4jnia56itigviazofy2ikjtvym/node_modules/expo-router/ios"
  ExpoImage:
    :path: "../node_modules/.pnpm/expo-image@2.1.7_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79.2_6ngynvfqyrde7r3tr6c67khpce/node_modules/expo-image/ios"
  ExpoKeepAwake:
    :path: "../node_modules/.pnpm/expo-keep-awake@14.1.4_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@_dvqn72nv4tnmxlkguzmsutlpsm/node_modules/expo-keep-awake/ios"
  ExpoLinking:
    :path: "../node_modules/.pnpm/expo-linking@7.1.5_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79_fgpdjjd25j2qavggaz6v6p5mfi/node_modules/expo-linking/ios"
  ExpoModulesCore:
    :path: "../node_modules/.pnpm/expo-modules-core@2.3.13/node_modules/expo-modules-core"
  ExpoSecureStore:
    :path: "../node_modules/.pnpm/expo-secure-store@14.2.3_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-nativ_b26lyp22cdohv2fyhpvbwyw2qq/node_modules/expo-secure-store/ios"
  ExpoSplashScreen:
    :path: "../node_modules/.pnpm/expo-splash-screen@0.30.8_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-nati_l4hgk35sh5ccwxwcvobjkqmq3a/node_modules/expo-splash-screen/ios"
  ExpoSymbols:
    :path: "../node_modules/.pnpm/expo-symbols@0.4.4_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79_my75exxaguaoj3lxhaupuz4ese/node_modules/expo-symbols/ios"
  ExpoSystemUI:
    :path: "../node_modules/.pnpm/expo-system-ui@5.0.7_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0._x2ru3hzuu4x3tcua4xmpfiebdq/node_modules/expo-system-ui/ios"
  ExpoWebBrowser:
    :path: "../node_modules/.pnpm/expo-web-browser@14.1.6_expo@53.0.9_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native_te7yqvz2kotvxpxbhzfcx4v7sy/node_modules/expo-web-browser/ios"
  fast_float:
    :podspec: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/third-party-podspecs/fast_float.podspec"
  FBLazyVector:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2025-03-03-RNv0.79.0-bc17d964d03743424823d7dd1a9f37633459c5c5
  RCT-Folly:
    :podspec: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/debug"
  React-defaultsnativemodule:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/nativemodule/defaults"
  React-domnativemodule:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/nativemodule/dom"
  React-Fabric:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon"
  React-FabricComponents:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/featureflags"
  React-featureflagsnativemodule:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/nativemodule/featureflags"
  React-graphics:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/hermes"
  React-idlecallbacksnativemodule:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks"
  React-ImageManager:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsinspectortracing:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/jsinspector-modern/tracing"
  React-jsitooling:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/jsitooling"
  React-jsitracing:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon"
  React-microtasksnativemodule:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/nativemodule/microtasks"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-NativeModulesApple:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-oscompat:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/oscompat"
  React-perflogger:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/reactperflogger"
  React-performancetimeline:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/performance/timeline"
  React-RCTActionSheet:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/React"
  React-RCTFBReactNativeSpec:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/Network"
  React-RCTRuntime:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/React/Runtime"
  React-RCTSettings:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/Libraries/Vibration"
  React-rendererconsistency:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/renderer/consistency"
  React-renderercss:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/renderer/css"
  React-rendererdebug:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon"
  React-RuntimeApple:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-timing:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/timing"
  React-utils:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/react/utils"
  ReactAppDependencyProvider:
    :path: build/generated/ios
  ReactCodegen:
    :path: build/generated/ios
  ReactCommon:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon"
  RNFlashList:
    :path: "../node_modules/@shopify/flash-list"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  Yoga:
    :path: "../node_modules/.pnpm/react-native@0.79.2_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0/node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 7e761d76ca2ce687f7cc98e698152abd03a18f90
  BVLinearGradient: cb006ba232a1f3e4f341bb62c42d1098c284da70
  DoubleConversion: cb417026b2400c8f53ae97020b2be961b59470cb
  EXConstants: 9f310f44bfedba09087042756802040e464323c0
  Expo: a9fc723f6c8f673f0e7e036c9021772d3a1a0707
  ExpoAsset: 3bc9adb7dbbf27ae82c18ca97eb988a3ae7e73b1
  ExpoBlur: 7f9379db213e4a56aeceda82e94a7fea1b1d8c48
  ExpoFileSystem: c36eb8155eb2381c83dda7dc210e3eec332368b6
  ExpoFont: abbb91a911eb961652c2b0a22eef801860425ed6
  ExpoHaptics: 0ff6e0d83cd891178a306e548da1450249d54500
  ExpoHead: af044f3e9c99e7d8d21bf653b4c2f2ef53a7f082
  ExpoImage: e7738bebc58cee17e93fa161f2444f9c343996f5
  ExpoKeepAwake: bf0811570c8da182bfb879169437d4de298376e7
  ExpoLinking: b85ff4eafeae6fc638c6cace60007ae521af0ef4
  ExpoModulesCore: 5d37821c36f3781dcd0ea9a393800c90eaa6259d
  ExpoSecureStore: b367d9f62c9102d808afbeb1561636d4276e439d
  ExpoSplashScreen: f524572afd81522e40850eaa7163e2ae99cce783
  ExpoSymbols: 5324de61341b396965a1cd3d2c738227b13d6a60
  ExpoSystemUI: 82c970cf8495449698e7343b4f78a0d04bcec9ee
  ExpoWebBrowser: 06fb5f767f53ad53944b068cdd207984cb998712
  fast_float: 06eeec4fe712a76acc9376682e4808b05ce978b6
  FBLazyVector: 84b955f7b4da8b895faf5946f73748267347c975
  fmt: a40bb5bd0294ea969aaaba240a927bd33d878cdd
  glog: 5683914934d5b6e4240e497e0f4a3b42d1854183
  hermes-engine: 314be5250afa5692b57b4dd1705959e1973a8ebe
  libavif: 84bbb62fb232c3018d6f1bab79beea87e35de7b7
  libdav1d: 23581a4d8ec811ff171ed5e2e05cd27bad64c39f
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  RCT-Folly: e78785aa9ba2ed998ea4151e314036f6c49e6d82
  RCTDeprecation: 83ffb90c23ee5cea353bd32008a7bca100908f8c
  RCTRequired: eb7c0aba998009f47a540bec9e9d69a54f68136e
  RCTTypeSafety: 659ae318c09de0477fd27bbc9e140071c7ea5c93
  React: c2d3aa44c49bb34e4dfd49d3ee92da5ebacc1c1c
  React-callinvoker: 1bdfb7549b5af266d85757193b5069f60659ef9d
  React-Core: 10597593fdbae06f0089881e025a172e51d4a769
  React-CoreModules: 6907b255529dd46895cf687daa67b24484a612c2
  React-cxxreact: a9f5b8180d6955bc3f6a3fcd657c4d9b4d95c1f6
  React-debug: e74e76912b91e08d580c481c34881899ccf63da9
  React-defaultsnativemodule: 11f6ee2cf69bf3af9d0f28a6253def33d21b5266
  React-domnativemodule: f940bbc4fa9e134190acbf3a4a9f95621b5a8f51
  React-Fabric: 6f5c357bf3a42ff11f8844ad3fc7a1eb04f4b9de
  React-FabricComponents: 10e0c0209822ac9e69412913a8af1ca33573379b
  React-FabricImage: f582e764072dfa4715ae8c42979a5bace9cbcc12
  React-featureflags: d5facceff8f8f6de430e0acecf4979a9a0839ba9
  React-featureflagsnativemodule: a7dd141f1ef4b7c1331af0035689fbc742a49ff4
  React-graphics: 36ae3407172c1c77cea29265d2b12b90aaef6aa0
  React-hermes: 9116d4e6d07abeb519a2852672de087f44da8f12
  React-idlecallbacksnativemodule: ae7f5ffc6cf2d2058b007b78248e5b08172ad5c3
  React-ImageManager: 9daee0dc99ad6a001d4b9e691fbf37107e2b7b54
  React-jserrorhandler: 1e6211581071edaf4ecd5303147328120c73f4dc
  React-jsi: 753ba30c902f3a41fa7f956aca8eea3317a44ee6
  React-jsiexecutor: 47520714aa7d9589c51c0f3713dfbfca4895d4f9
  React-jsinspector: cfd27107f6d6f1076a57d88c932401251560fe5f
  React-jsinspectortracing: 76a7d791f3c0c09a0d2bf6f46dfb0e79a4fcc0ac
  React-jsitooling: 995e826570dd58f802251490486ebd3244a037ab
  React-jsitracing: 094ae3d8c123cea67b50211c945b7c0443d3e97b
  React-logger: 8edfcedc100544791cd82692ca5a574240a16219
  React-Mapbuffer: c3f4b608e4a59dd2f6a416ef4d47a14400194468
  React-microtasksnativemodule: 054f34e9b82f02bd40f09cebd4083828b5b2beb6
  react-native-safe-area-context: 562163222d999b79a51577eda2ea8ad2c32b4d06
  react-native-webview: 520bcb79c3f2af91e157cdd695732a34ab5f25c8
  React-NativeModulesApple: 2c4377e139522c3d73f5df582e4f051a838ff25e
  React-oscompat: ef5df1c734f19b8003e149317d041b8ce1f7d29c
  React-perflogger: 9a151e0b4c933c9205fd648c246506a83f31395d
  React-performancetimeline: 5b0dfc0acba29ea0269ddb34cd6dd59d3b8a1c66
  React-RCTActionSheet: a499b0d6d9793886b67ba3e16046a3fef2cdbbc3
  React-RCTAnimation: cc64adc259aabc3354b73065e2231d796dfce576
  React-RCTAppDelegate: 9d523da768f1c9e84c5f3b7e3624d097dfb0e16b
  React-RCTBlob: e727f53eeefded7e6432eb76bd22b57bc880e5d1
  React-RCTFabric: 58590aa4fdb4ad546c06a7449b486cf6844e991f
  React-RCTFBReactNativeSpec: 9064c63d99e467a3893e328ba3612745c3c3a338
  React-RCTImage: 7159cbdbb18a09d97ba1a611416eced75b3ccb29
  React-RCTLinking: 46293afdb859bccc63e1d3dedc6901a3c04ef360
  React-RCTNetwork: 4a6cd18f5bcd0363657789c64043123a896b1170
  React-RCTRuntime: 5ab904fd749aa52f267ef771d265612582a17880
  React-RCTSettings: 61e361dc85136d1cb0e148b7541993d2ee950ea7
  React-RCTText: abd1e196c3167175e6baef18199c6d9d8ac54b4e
  React-RCTVibration: 490e0dcb01a3fe4a0dfb7bc51ad5856d8b84f343
  React-rendererconsistency: 351fdbc5c1fe4da24243d939094a80f0e149c7a1
  React-renderercss: 3438814bee838ae7840a633ab085ac81699fd5cf
  React-rendererdebug: 0ac2b9419ad6f88444f066d4b476180af311fb1e
  React-rncore: 57ed480649bb678d8bdc386d20fee8bf2b0c307c
  React-RuntimeApple: 8b7a9788f31548298ba1990620fe06b40de65ad7
  React-RuntimeCore: e03d96fbd57ce69fd9bca8c925942194a5126dbc
  React-runtimeexecutor: d60846710facedd1edb70c08b738119b3ee2c6c2
  React-RuntimeHermes: aab794755d9f6efd249b61f3af4417296904e3ba
  React-runtimescheduler: c3cd124fa5db7c37f601ee49ca0d97019acd8788
  React-timing: a90f4654cbda9c628614f9bee68967f1768bd6a5
  React-utils: a612d50555b6f0f90c74b7d79954019ad47f5de6
  ReactAppDependencyProvider: 04d5eb15eb46be6720e17a4a7fa92940a776e584
  ReactCodegen: 16f7a4c339dd030d72cce908369547c1e1477cc2
  ReactCommon: 76d2dc87136d0a667678668b86f0fca0c16fdeb0
  RNFlashList: 7ad51f0d0d51a3b7b1d1bb07947b927cb352afc4
  RNGestureHandler: 7d0931a61d7ba0259f32db0ba7d0963c3ed15d2b
  RNReanimated: 9576d4d9865cdd3b5a304bcb366d37c43354a33a
  RNScreens: 5621e3ad5a329fbd16de683344ac5af4192b40d3
  RNSVG: 794f269526df9ddc1f79b3d1a202b619df0368e3
  RNVectorIcons: 941a39b5d3b9d8cf8ac2e2fc09b07bfafbcf9796
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  SDWebImageAVIFCoder: 00310d246aab3232ce77f1d8f0076f8c4b021d90
  SDWebImageSVGCoder: 15a300a97ec1c8ac958f009c02220ac0402e936c
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  SocketRocket: d4aabe649be1e368d1318fdf28a022d714d65748
  Yoga: c758bfb934100bb4bf9cbaccb52557cee35e8bdf

PODFILE CHECKSUM: 263cae3093a5bbaa98f94d324ec49f5d7600ae24

COCOAPODS: 1.16.2
