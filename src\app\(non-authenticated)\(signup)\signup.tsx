import AppleIcon from '@assets/svgs/apple-icon.svg';
import GoogleIcon from '@assets/svgs/google-icon.svg';
import {Button, Text, TextInput, View} from '@components/native';
import {StackBackButton} from '@components/shared';
import {BounceTap} from '@components/shared/animated';
import useStitch from '@packages/useStitch';
import CONST from 'expo-constants';
import {router} from 'expo-router';
import React, {useState} from 'react';
import {Keyboard, TouchableWithoutFeedback, useWindowDimensions} from 'react-native';

const SignupScreen = () => {
    const {isMutating, mutate} = useStitch('signup');
    const [email, setEmail] = useState('');

    const {height} = useWindowDimensions();
    // () => router.push("/verify-otp?email=<EMAIL>")

    return (
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View
                flex={1}
                p={20}
                display="flex"
                jc="space-evenly"
            >
                {router.canGoBack() && (
                    <View
                        pos="absolute"
                        top={CONST.statusBarHeight + 10}
                        left={15}
                    >
                        <StackBackButton title="Sign up" />
                    </View>
                )}
                <View
                    flexCenterColumn
                    mt={CONST.statusBarHeight}
                    gap={20}
                >
                    <Text
                        fw="500"
                        color="neutral80"
                        ta="center"
                        ff="PlayfairMedium"
                        fs="26"
                    >
                        Create an account to {`\n`} get stitched.
                    </Text>
                </View>
                <View>
                    <TextInput
                        label="Email"
                        keyboardType="email-address"
                        value={email}
                        onSuffixIconPress={() => setEmail('')}
                        onChangeText={setEmail}
                    />
                    <Button
                        isLoading={isMutating}
                        mt={30}
                        onPress={() => {
                            mutate({email});
                        }}
                    >
                        SIGN UP
                    </Button>
                </View>
                <View
                    mt={20}
                    gap={20}
                    display="flex"
                    ai="center"
                    jc="flex-end"
                >
                    <View
                        disableSizeMatter
                        bg="neutral30"
                        w={'100%'}
                        h={1}
                    />
                    <Text
                        fw="500"
                        color="neutral80"
                        fs="12"
                    >
                        Or continue with
                    </Text>
                    <View
                        flexCenterRow
                        gap={14}
                    >
                        <BounceTap onPress={() => {}}>
                            <View
                                br={100}
                                h={45}
                                flexCenterRow
                                disableSizeMatter
                                w={45}
                                bg="purpleLight"
                            >
                                <GoogleIcon />
                            </View>
                        </BounceTap>
                        <BounceTap onPress={() => {}}>
                            <View
                                br={100}
                                h={45}
                                flexCenterRow
                                disableSizeMatter
                                w={45}
                                bg="purpleLight"
                            >
                                <AppleIcon />
                            </View>
                        </BounceTap>
                    </View>
                    <Text
                        fs="12"
                        mt={30}
                    >
                        ALREADY HAVE AN ACCOUNT ?
                        <Text
                            onPress={() => router.replace('/login')}
                            fs="12"
                            style={{color: '#8E97FD'}}
                        >
                            {' '}
                            LOG IN
                        </Text>
                    </Text>
                </View>
            </View>
        </TouchableWithoutFeedback>
    );
};

export default SignupScreen;
