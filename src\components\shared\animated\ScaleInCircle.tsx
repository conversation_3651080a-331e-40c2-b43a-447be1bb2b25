import { useTheme } from "@context";
import { ThemeColorKeys } from "@types";
import React, { ReactNode } from "react";
import { StyleSheet, View } from "react-native";
import { Pressable } from "react-native-gesture-handler";
// require("../../../../docs/previews/ScaleInCircle.png");
import Animated, {
  useAnimatedStyle,
  withSpring,
} from "react-native-reanimated";

interface ScaleInCircleProps {
  children: ReactNode;
  active?: boolean;
  bg?: ThemeColorKeys;
  br?: number;
  onTapScale?: number;
  onPress?: () => void;
  h?: number;
  w?: number;
}

/**
 * Usage : Can be used in selecting Avatar or Icon in a list/grid
 * @param {ScaleInCircleProps} props
 * @param {ReactNode} props.children - The content to be displayed inside the circle.
 * @param {boolean} [props.active=false] - Whether the circle is active or not.
 * @param {ThemeColorKeys} [props.bg="purple800"] - Background color of the circle.
 * @param {number} [props.br=100] - Border radius of the circle.
 * @param {number} [props.onTapScale=1.05] - Scale factor when the circle is tapped.
 * @param {() => void} [props.onPress] - Function to call when the circle is pressed. 
 * @returns {JSX.Element} - A circle that scales in when pressed.
 * * ![ScaleInCircle Component](https://picsum.photos/200/300)
*/
export const ScaleInCircle: React.FC<ScaleInCircleProps> = ({
  children,
  active = false,
  bg = "purple800",
  br = 100,
  onTapScale = 1.05,
  h = 50,
  w = 50,
  onPress,
}) => {
  const { colors } = useTheme();
  const animatedStyles = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: withSpring(active ? onTapScale : 1, { stiffness: 200 }) },
        { translateY: withSpring(active ? -5 : 0, { stiffness: 200 }) },
      ],
    };
  }, [active]);

  return (
    <Pressable onPress={onPress}>
      <View style={[styles.container, { height: h, width: w, backgroundColor: colors[bg], borderRadius: br }]}>
        <Animated.View
          style={[
            animatedStyles,
          ]}
        >
          {children}
        </Animated.View>
      </View>
    </Pressable>
  );
};


const styles = StyleSheet.create({
  container: {
    justifyContent: "center",
    alignItems: "center",
    padding: 8,
    display: "flex",
  },
});
