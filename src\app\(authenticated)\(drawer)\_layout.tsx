import BookmarkIcon from '@assets/svgs/bookmark-icon.svg';
import CloseIcon from '@assets/svgs/close-icon.svg';
import FaqIcon from '@assets/svgs/faq-icon.svg';
import FormIcon from '@assets/svgs/form-icon.svg';
import HamburgerBg from '@assets/svgs/hamburger-bg.svg';
import InfoIcon from '@assets/svgs/info-icon.svg';
import LogoutIcon from '@assets/svgs/logout-icon.svg';
import PrivacyPolicyIcon from '@assets/svgs/privacy-policy-icon.svg';
import SettingsIcon from '@assets/svgs/settings-icon.svg';
import TosIcon from '@assets/svgs/tos-icon.svg';
import {Text, View} from '@components/native';
import {Assets} from '@constants';
import {useTheme} from '@context';
import {DrawerContentComponentProps, DrawerContentScrollView} from '@react-navigation/drawer';
import {router, usePathname} from 'expo-router';
import {Drawer} from 'expo-router/drawer';
import React, {useEffect} from 'react';
import {Image, Pressable} from 'react-native';

const DrawerItem = ({Icon, label, onPress = () => {}, noDivider = false, mt = 0}: {Icon: React.ComponentType; label: string; onPress?: () => void; noDivider?: boolean; mt?: number}) => (
    <>
        <Pressable onPress={onPress}>
            <View
                fd="row"
                ai="center"
                mt={mt}
            >
                <Icon />
                <Text
                    color="neutral70"
                    fw="500"
                    fs="14"
                    ml={16}
                >
                    {label}
                </Text>
            </View>
        </Pressable>
        {!noDivider && (
            <View
                h={1}
                bg="neutral00"
                w="100%"
                my={16}
            />
        )}
    </>
);

const CustomDrawerContent = (props: DrawerContentComponentProps) => {
    const pathname = usePathname();
    const {colors} = useTheme();

    useEffect(() => {
        console.log(pathname);
    }, [pathname]);

    return (
        <DrawerContentScrollView
            style={{backgroundColor: colors.background}}
            contentContainerStyle={{flexGrow: 1}}
            {...props}
        >
            <View>
                <HamburgerBg width="100%" />
                {/* Close icon */}
                <Pressable
                    style={{position: 'absolute', top: 26, right: 21, zIndex: 2}}
                    onPress={() => router.back()}
                >
                    <CloseIcon
                        width={24}
                        height={24}
                    />
                </Pressable>
                {/* Avatar and user info */}
                <View
                    pos="absolute"
                    top={95}
                    left={20}
                    fd="row"
                    ai="center"
                >
                    <Image
                        source={Assets.placeholder.avatar}
                        style={{width: 72, height: 72, borderRadius: 100}}
                        resizeMode="cover"
                    />
                    <View ml={15}>
                        <Text
                            color="neutral80"
                            fw="400"
                            fs="10"
                        >
                            Lucas Benjamin Scott
                        </Text>
                        <Text
                            color="neutral80"
                            fw="600"
                            fs="12"
                        >
                            @luca_b_2025
                        </Text>
                    </View>
                </View>
            </View>
            {/* Top menu */}
            <View
                pt={32}
                px={20}
                pb={16}
            >
                <DrawerItem
                    Icon={FormIcon}
                    label="Update Questionnaire"
                    onPress={() => {}}
                />
                <DrawerItem
                    Icon={BookmarkIcon}
                    label="Bookmark"
                    onPress={() => {}}
                />
                <DrawerItem
                    Icon={FaqIcon}
                    label="FAQs"
                    onPress={() => {}}
                />
                <DrawerItem
                    Icon={InfoIcon}
                    label="About"
                    onPress={() => {}}
                />
                <DrawerItem
                    Icon={SettingsIcon}
                    label="Settings"
                    noDivider
                    onPress={() => {}}
                />
            </View>
            {/* Bottom menu */}
            <View style={{flex: 1}} />
            <View px={20}>
                <DrawerItem
                    Icon={LogoutIcon}
                    label="Logout"
                    noDivider
                    onPress={() => {}}
                />
                <DrawerItem
                    Icon={PrivacyPolicyIcon}
                    label="Privacy Policy"
                    noDivider
                    mt={32}
                    onPress={() => {}}
                />
                <DrawerItem
                    Icon={TosIcon}
                    label="Terms of Services"
                    noDivider
                    mt={32}
                    onPress={() => {}}
                />
            </View>
        </DrawerContentScrollView>
    );
};

export default function Layout() {
    return (
        <Drawer
            drawerContent={(props) => <CustomDrawerContent {...props} />}
            screenOptions={{headerShown: false}}
        />
    );
}
