import CommentIcon from '@assets/svgs/comment-icon.svg';
import HashtagIcon from '@assets/svgs/hashtag-icon.svg';
import HeartIcon from '@assets/svgs/heart-icon.svg';
import OptionsIcon from '@assets/svgs/options-icon.svg';
import PollIcon from '@assets/svgs/poll-icon.svg';
import SaveIcon from '@assets/svgs/save-icon.svg';
import ThoughtBalloonIcon from '@assets/svgs/thought-balloon-icon.svg';
import {Button, Text, View} from '@components/native';
import {ScreenWrapper} from '@components/shared';
import {AnimatedFlashList} from '@components/shared/animated';
import {Assets} from '@constants';
import {useTheme} from '@context';
import BottomSheet, {BottomSheetScrollView} from '@gorhom/bottom-sheet';
import {useRouter} from 'expo-router';
import React, {useCallback, useMemo, useRef, useState} from 'react';
import {Image, Pressable} from 'react-native';

interface Comment {
    username: string;
    avatar: any;
    timeAgo: string;
    content: string;
    replyCount: number;
}

interface Post {
    username: string;
    avatar: any;
    timeAgo: string;
    likeCount: number;
    commentCount: number;
    postTitle: string;
    postContent: string;
    hashtags?: string[];
    comments: Comment[];
}

const MOCK_POSTS: Post[] = [
    {
        username: 'travel_guru',
        avatar: Assets.placeholder.feedAvatar1,
        timeAgo: '10 mins ago',
        likeCount: 120,
        commentCount: 2,
        postTitle: 'Exploring the Alps!',
        postContent: 'Just finished hiking the beautiful Alps. The view from the top is breathtaking! Highly recommend to all adventure lovers.',
        hashtags: ['#Alps', '#Adventure', '#Travel'],
        comments: [
            {
                username: 'mountain_lover',
                avatar: Assets.placeholder.commentAvatar1,
                timeAgo: '5 mins ago',
                content: 'Wow, looks amazing! How long was the hike?',
                replyCount: 0,
            },
            {
                username: 'nature_fan',
                avatar: Assets.placeholder.commentAvatar2,
                timeAgo: '3 mins ago',
                content: 'The Alps are on my bucket list! Thanks for sharing.',
                replyCount: 0,
            },
        ],
    },
    {
        username: 'foodie_explorer',
        avatar: Assets.placeholder.feedAvatar2,
        timeAgo: '1 hour ago',
        likeCount: 89,
        commentCount: 1,
        postTitle: 'Best Pizza in Naples',
        postContent: 'Tried the most delicious pizza in Naples today. The crust was perfect and the cheese was so fresh!',
        hashtags: ['#Foodie', '#Pizza', '#Naples'],
        comments: [
            {
                username: 'pizza_lover',
                avatar: Assets.placeholder.commentAvatar3,
                timeAgo: '45 mins ago',
                content: 'I need to try this place! Where exactly is it?',
                replyCount: 0,
            },
        ],
    },
    {
        username: 'wellness_journey',
        avatar: Assets.placeholder.feedAvatar3,
        timeAgo: '2 days ago',
        likeCount: 200,
        commentCount: 2,
        postTitle: 'Morning Yoga Routine',
        postContent: 'Started a new morning yoga routine and it has changed my life. Feeling more energetic and focused every day.',
        hashtags: ['#Yoga', '#Wellness', '#Routine'],
        comments: [
            {
                username: 'yogi_life',
                avatar: Assets.placeholder.commentAvatar1,
                timeAgo: '1 day ago',
                content: 'So inspiring! Can you share your routine?',
                replyCount: 0,
            },
            {
                username: 'fit_mom',
                avatar: Assets.placeholder.commentAvatar2,
                timeAgo: '1 day ago',
                content: 'Love this! I need to get back to yoga.',
                replyCount: 0,
            },
        ],
    },
];

interface Poll {
    username: string;
    avatar: any;
    timeAgo: string;
    question: string;
    options: string[];
    votes: number[];
    voted: number | undefined;
}

const MOCK_POLLS: Poll[] = [
    {
        username: 'poll_creator1',
        avatar: Assets.placeholder.feedAvatar1,
        timeAgo: '1 hour ago',
        question: 'Which mobile operating system do you prefer?',
        options: ['iOS', 'Android'],
        votes: [30, 27],
        voted: undefined,
    },
    {
        username: 'poll_creator2',
        avatar: Assets.placeholder.feedAvatar2,
        timeAgo: '2 hours ago',
        question: 'What is your favorite food?',
        options: ['Pizza', 'Burger', 'Sushi', 'Pasta'],
        votes: [15, 10, 20, 5],
        voted: undefined,
    },
];

const FeedCard = ({post, onShowComments}: {post: Post; onShowComments: () => void}) => {
    const {colors} = useTheme();
    return (
        <View
            px={8}
            pb={24}
        >
            <View
                bg="white"
                br={20}
                bw={1}
                bc="purpleLight"
            >
                <View
                    px={13}
                    py={16}
                >
                    <View
                        fd="row"
                        ai="center"
                        mb={16}
                        jc="space-between"
                    >
                        <View
                            fd="row"
                            ai="center"
                        >
                            <Image
                                width={40}
                                height={40}
                                borderRadius={20}
                                source={post.avatar}
                                style={{marginRight: 12, borderWidth: 1.6, borderColor: colors.neutral10}}
                            />
                            <View>
                                <Text
                                    ff="PoppinsRegular"
                                    fs="16"
                                    fw="700"
                                >
                                    @{post.username}
                                </Text>
                                <Text
                                    ff="PoppinsRegular"
                                    fs="12"
                                    color="neutral60"
                                    mt={4}
                                >
                                    {post.timeAgo}
                                </Text>
                            </View>
                        </View>
                        <OptionsIcon
                            width={6}
                            height={15}
                        />
                    </View>

                    <Text
                        ff="PoppinsRegular"
                        fw="700"
                        fs="18"
                    >
                        {post.postTitle}
                    </Text>
                    <Text
                        ff="PoppinsRegular"
                        mt={8}
                    >
                        {post.postContent}
                    </Text>

                    {post.hashtags && post.hashtags.length > 0 && (
                        <Text
                            ff="PoppinsRegular"
                            color="purple500"
                            fw="600"
                            fs="14"
                        >
                            {post.hashtags.join(' ')}
                        </Text>
                    )}

                    <View
                        mt={16}
                        fd="row"
                        ai="center"
                        jc="space-between"
                    >
                        <View
                            fd="row"
                            ai="center"
                        >
                            <HeartIcon
                                width={24}
                                height={24}
                                style={{marginRight: 6}}
                            />
                            <Text
                                mr={16}
                                fs="14"
                            >
                                {post.likeCount}
                            </Text>
                            <Pressable
                                onPress={onShowComments}
                                style={{flexDirection: 'row', alignItems: 'center'}}
                            >
                                <CommentIcon
                                    width={24}
                                    height={24}
                                    style={{marginRight: 6}}
                                />
                                <Text fs="14">{post.commentCount}</Text>
                            </Pressable>
                        </View>
                        <View>
                            <SaveIcon
                                width={24}
                                height={24}
                            />
                        </View>
                    </View>
                </View>
            </View>
        </View>
    );
};

const PollCard = ({poll}: {poll: Poll}) => {
    const {colors} = useTheme();
    const [voted, setVoted] = useState<number | undefined>(poll.voted);
    const [votes, setVotes] = useState<number[]>(poll.votes);

    const handleVote = (index: number) => {
        if (voted !== undefined) return;
        const newVotes = [...votes];
        newVotes[index] += 1;
        setVotes(newVotes);
        setVoted(index);
    };

    const totalVotes = votes.reduce((a, b) => a + b, 0);
    const getVotePercentage = (index: number) => Math.round((votes[index] / totalVotes) * 100);

    return (
        <View
            px={8}
            pb={24}
        >
            <View
                bg="white"
                br={20}
                bw={1}
                bc="purpleLight"
            >
                <View
                    px={13}
                    py={16}
                >
                    <View
                        fd="row"
                        ai="center"
                        mb={16}
                        jc="space-between"
                    >
                        <View
                            fd="row"
                            ai="center"
                        >
                            <Image
                                width={40}
                                height={40}
                                borderRadius={20}
                                source={poll.avatar}
                                style={{marginRight: 12, borderWidth: 1.6, borderColor: colors.neutral10}}
                            />
                            <View>
                                <Text
                                    ff="PoppinsRegular"
                                    fs="16"
                                    fw="700"
                                >
                                    @{poll.username}
                                </Text>
                                <Text
                                    ff="PoppinsRegular"
                                    fs="12"
                                    color="neutral60"
                                    mt={4}
                                >
                                    {poll.timeAgo}
                                </Text>
                            </View>
                        </View>
                        <OptionsIcon
                            width={6}
                            height={15}
                        />
                    </View>

                    <View
                        fd="row"
                        fw="wrap"
                        ai="center"
                    >
                        <Text
                            ff="PoppinsRegular"
                            fw="700"
                            fs="18"
                        >
                            {poll.question}
                        </Text>
                        {/* <View
                            bg="lightBlue"
                            m={5}
                            px={6}
                            py={3}
                            br={6}
                        >
                            <Text
                                ff="PoppinsRegular"
                                fw="400"
                                fs="12"
                            >
                                poll
                            </Text>
                        </View> */}
                    </View>

                    {poll.options.map((option, index) => (
                        <Pressable
                            key={index}
                            onPress={() => handleVote(index)}
                            disabled={voted !== undefined}
                        >
                            <View
                                bg="purple100"
                                jc="center"
                                h={48}
                                br={10}
                                mt={12}
                            >
                                {voted !== undefined && (
                                    <View
                                        bg="purple300"
                                        h={48}
                                        w={`${getVotePercentage(index)}%`}
                                        br={10}
                                        pos="absolute"
                                        left={0}
                                    />
                                )}
                                <View
                                    fd="row"
                                    ai="center"
                                    jc="space-between"
                                    px={21}
                                >
                                    <Text
                                        key={'option-' + index}
                                        fw="500"
                                        fs="16"
                                    >
                                        {option}
                                    </Text>
                                    {voted !== undefined && (
                                        <View
                                            fd="row"
                                            ai="center"
                                        >
                                            {voted === index && (
                                                <Image
                                                    source={Assets.placeholder.avatar}
                                                    style={{width: 18, height: 18, borderRadius: 30, marginRight: 9}}
                                                />
                                            )}
                                            <Text
                                                fw="700"
                                                fs="16"
                                                color="neutral80"
                                            >
                                                {getVotePercentage(index)}%
                                            </Text>
                                        </View>
                                    )}
                                </View>
                            </View>
                        </Pressable>
                    ))}

                    <View h={23}>
                        {voted !== undefined && (
                            <View
                                fd="row"
                                ai="center"
                                jc="space-between"
                                pos="absolute"
                                style={{bottom: 0, left: 0, right: 0}}
                            >
                                <Text
                                    fw="500"
                                    fs="12"
                                    color="neutral40"
                                >
                                    {totalVotes} responses
                                </Text>
                                <Pressable
                                    onPress={() => {
                                        if (voted !== undefined) {
                                            const newVotes = [...votes];
                                            newVotes[voted] -= 1;
                                            setVotes(newVotes);
                                            setVoted(undefined);
                                        }
                                    }}
                                >
                                    <Text
                                        fw="600"
                                        fs="12"
                                        color="neutral50"
                                    >
                                        Remove vote?
                                    </Text>
                                </Pressable>
                            </View>
                        )}
                    </View>
                </View>
            </View>
        </View>
    );
};

const FeedTabScreen = () => {
    const bottomSheetRef = useRef<BottomSheet>(null);
    const snapPoints = useMemo(() => ['45%', '85%'], []);
    const [selectedPost, setSelectedPost] = useState<Post | null>(null);
    const profileCompletion = 60;
    const {push} = useRouter();

    const handleShowComments = useCallback((post: Post) => {
        setSelectedPost(post);
        setTimeout(() => {
            bottomSheetRef.current?.snapToIndex(1);
        }, 0);
    }, []);

    const handleSheetChanges = useCallback((index: number) => {
        if (index === -1) setSelectedPost(null);
    }, []);

    return (
        <ScreenWrapper>
            <AnimatedFlashList
                ListHeaderComponent={
                    <View
                        px={8}
                        py={16}
                    >
                        <Pressable
                            onPress={() => {
                                push({
                                    pathname: '/(authenticated)/(screens)/post',
                                    params: {
                                        hashtag: false,
                                        poll: false,
                                    },
                                });
                            }}
                        >
                            <View
                                px={20}
                                py={18}
                                bg="white"
                                br={20}
                                style={{
                                    boxShadow: '0px 4px 10px rgba(0,0,0,0.12)',
                                    shadowColor: '#000',
                                    shadowOffset: {width: 0, height: 4},
                                    shadowOpacity: 0.12,
                                    shadowRadius: 10,
                                }}
                            >
                                <View
                                    fd="row"
                                    ai="center"
                                    jc="space-between"
                                    mb={16}
                                >
                                    <Text
                                        fs="16"
                                        fw="500"
                                        color="neutral60"
                                    >
                                        What’s on your mind today?
                                    </Text>
                                    <ThoughtBalloonIcon
                                        width={43}
                                        height={43}
                                    />
                                </View>
                                <View
                                    fd="row"
                                    ai="center"
                                >
                                    <Pressable onPress={() => {}}>
                                        <HashtagIcon
                                            width={16}
                                            height={16}
                                            style={{marginRight: 25}}
                                        />
                                    </Pressable>
                                    <Pressable onPress={() => {}}>
                                        <PollIcon
                                            width={16}
                                            height={16}
                                            style={{marginRight: 25}}
                                        />
                                    </Pressable>
                                </View>
                            </View>
                        </Pressable>
                        {profileCompletion < 100 && (
                            <View
                                h={237}
                                mt={11}
                                pos="relative"
                            >
                                <View
                                    pos="absolute"
                                    style={{bottom: 0, left: 0, right: 0}}
                                    h={200}
                                    bg="lightBlue"
                                    br={20}
                                    bw={1}
                                    bc="purpleLight"
                                    px={13}
                                    pb={13}
                                    pt={23}
                                >
                                    <View
                                        fd="row"
                                        ai="flex-start"
                                    >
                                        <View>
                                            <Text
                                                ff="PlayfairRegular"
                                                fw="700"
                                                fs="20"
                                                color="purple700"
                                            >
                                                Let's Personalize{'\n'}Your Journey 💫
                                            </Text>
                                            <Text
                                                mt={19}
                                                fw="400"
                                                fs="12"
                                                color="neutral60"
                                            >
                                                A few more details will help us find{'\n'}people who truly get you.
                                            </Text>
                                        </View>
                                    </View>
                                    <View
                                        fd="row"
                                        jc="space-between"
                                        mt={15}
                                        mb={25}
                                    >
                                        <View
                                            flex={1}
                                            bg="background"
                                            br={20}
                                            bw={1}
                                            bc="neutral10"
                                            h={10}
                                        >
                                            <View
                                                flex={1}
                                                w={`${profileCompletion}%`}
                                                bg="purple300"
                                                br={20}
                                            />
                                        </View>
                                        <Text
                                            ff="PoppinsRegular"
                                            fw="400"
                                            fs="10"
                                            color="neutral70"
                                            ml={9}
                                        >
                                            {profileCompletion}% complete, Almost there...
                                        </Text>
                                    </View>
                                    <Button
                                        isFullWidth={true}
                                        onPress={() => {}}
                                    >
                                        Update profile
                                    </Button>
                                </View>
                                <View
                                    pos="absolute"
                                    style={{top: 0, right: 0}}
                                >
                                    <Image source={Assets.images.completeProfile} />
                                </View>
                            </View>
                        )}
                    </View>
                }
                data={[...MOCK_POSTS, ...MOCK_POLLS]}
                renderItem={({item}: {item: Post | Poll}) => {
                    if ('postTitle' in item) {
                        return (
                            <FeedCard
                                post={item}
                                onShowComments={() => handleShowComments(item)}
                            />
                        );
                    }
                    return <PollCard poll={item} />;
                }}
            />
            <BottomSheet
                ref={bottomSheetRef}
                index={-1}
                snapPoints={snapPoints}
                onChange={handleSheetChanges}
                enablePanDownToClose
                style={{borderRadius: 24}}
            >
                <View
                    px={20}
                    py={25}
                    flex={1}
                    jc="flex-end"
                >
                    <Text
                        fs="24"
                        fw="600"
                        mb={24}
                        ta="left"
                        color="neutral80"
                    >
                        Comments
                    </Text>
                    <BottomSheetScrollView showsVerticalScrollIndicator={false}>
                        {selectedPost?.comments.map((comment, index) => (
                            <View
                                key={'comment-' + index}
                                mb={18}
                            >
                                <View
                                    fd="row"
                                    ai="center"
                                    mb={0}
                                >
                                    <Image
                                        width={36}
                                        height={36}
                                        borderRadius={18}
                                        source={comment.avatar}
                                    />
                                    <View w={6} />
                                    <Text
                                        fw="600"
                                        fs="14"
                                        color="neutral80"
                                    >
                                        {comment.username}
                                    </Text>
                                    <View flex={1} />
                                    <Text
                                        color="neutral50"
                                        fs="10"
                                    >
                                        {comment.timeAgo}
                                    </Text>
                                </View>
                                <View
                                    fd="row"
                                    ai="flex-start"
                                >
                                    <View w={36} />
                                    <View flex={1}>
                                        <Text
                                            fs="10"
                                            color="neutral70"
                                            mt={2}
                                        >
                                            {comment.content}
                                        </Text>
                                        <View h={8} />
                                        <View
                                            fd="row"
                                            ai="center"
                                        >
                                            <Text
                                                fs="12"
                                                color="neutral70"
                                                fw="700"
                                            >
                                                Reply
                                            </Text>
                                            <View
                                                w={2}
                                                h={2}
                                                mx={5}
                                                bg="neutral80"
                                                br={100}
                                            />
                                            <Text
                                                fw="400"
                                                fs="10"
                                                color="neutral50"
                                            >
                                                Show {comment.replyCount} {comment.replyCount === 1 ? 'reply' : 'replies'}
                                            </Text>
                                        </View>
                                    </View>
                                </View>
                            </View>
                        ))}
                    </BottomSheetScrollView>
                </View>
            </BottomSheet>
        </ScreenWrapper>
    );
};

export default FeedTabScreen;
