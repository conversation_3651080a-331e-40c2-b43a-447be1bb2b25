// ThemeContext.tsx
import useStitch from '@packages/useStitch';
import {ExampleAutoLoginUserResponse, ExampleUserSessionData} from '@packages/useStitch/mock/example-types';
import {ApiResponse} from '@packages/useStitch/types/common-api-types';
import {useRouter} from 'expo-router';
import React, {createContext, useContext, useEffect, useState} from 'react';

type AuthStateType = {
    isAuthenticated: boolean;
    session: null | ExampleUserSessionData;
    isLoading: boolean;
};

const initialAuthValue = {
    isAuthenticated: false,
    session: null,
} as AuthStateType;

const AuthContext = createContext<AuthStateType | undefined>(undefined);

export const AuthProvider: React.FC = ({children}) => {
    const [authState, setAuthState] = useState<AuthStateType>(initialAuthValue);

    const onAutoLoginSuccess = (data: ApiResponse) => {
        const {accessToken, refreshToken, user} = data.data;
        setAuthState({
            isAuthenticated: true,
            isLoading: false,
            session: {
                accessToken: accessToken,
                user: {
                    avatar: '',
                    gender: 'male',
                    moodEnabled: true,
                    name: 'Test User',
                    totalHealedCount: 20,
                    totalPostCount: 32,
                    username: 'dynamic :) ',
                    age: 90.5,
                    bio: 'Markdown bio here',
                    location: 'Texas , United States',
                },
            },
        });
    };

    const {isLoading} = useStitch('exampleAutoLogin', {mutationOptions: {onSuccess: onAutoLoginSuccess}});
    const {dismissTo, push} = useRouter();

    useEffect(() => {
        push('/feed');
    }, []);

    return <AuthContext.Provider value={{isLoading, isAuthenticated: authState.isAuthenticated, session: authState.session}}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthStateType => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within a AuthProvider');
    }
    return context;
};
