import { useTheme } from "@context";
import { ThemeColorKeys } from "@types";
import React, { ReactNode } from "react";
import { StyleSheet } from "react-native";
import { Pressable } from "react-native-gesture-handler";
import Animated, {
    useAnimatedStyle,
    useSharedValue,
    withSpring,
    withTiming,
} from "react-native-reanimated";

interface BounceTapProps {
  children: ReactNode;
  bg?: ThemeColorKeys;
  br?: number;
  onPress?: () => void;
  h?: number;
  w?: number;
  /**
   * The scale factor when the component is pressed (e.g., 0.95 for a slight shrink).
   * @default 0.95
   */
  pressedScale?: number;
  /**
   * The stiffness of the spring animation for the bounce effect.
   * @default 200
   */
  springStiffness?: number;
  /**
   * The damping of the spring animation for the bounce effect.
   * @default 17
   */
  springDamping?: number;
  /**
   * The duration of the timing animation when the press ends.
   * @default 100
   */
  releaseDuration?: number;
  /**
   * Whether the component should only trigger animation on long press.
   * @default false
   */
  onLongPress?: () => void;
  delayLongPress?: number;
}

/**
 * Usage : Creates a tappable component with a customizable bounce-in animation on press.
 * @param {BounceTapProps} props
 * @param {ReactNode} props.children - The content to be displayed inside the component.
 * @param {ThemeColorKeys} [props.bg="transparent"] - Background color of the component.
 * @param {number} [props.br=0] - Border radius of the component.
 * @param {() => void} [props.onPress] - Function to call when the component is pressed.
 * @param {number} [props.h] - Optional fixed height of the component.
 * @param {number} [props.w] - Optional fixed width of the component.
 * @param {number} [props.pressedScale=0.95] - The scale factor when the component is pressed.
 * @param {number} [props.springStiffness=200] - The stiffness of the spring animation.
 * @param {number} [props.springDamping=17] - The damping of the spring animation.
 * @param {number} [props.releaseDuration=100] - The duration of the release animation.
 * @param {() => void} [props.onLongPress] - Function to call when the component is long pressed.
 * @param {number} [props.delayLongPress] - Delay in ms for long press.
 * @returns {JSX.Element} - A component that bounces in when pressed.
 *
 * ![BounceTap Component Preview](https://i.imgur.com/your-bouncetap-preview.gif)
 */
export const BounceTap: React.FC<BounceTapProps> = ({
  children,
  bg = "transparent", // Default to transparent as it's a tap wrapper
  br = 0, // Default to 0 for a general component
  onPress,
  h, // Make height and width optional
  w,
  pressedScale = 0.95, // Default slight shrink
  springStiffness = 200,
  springDamping = 17,
  releaseDuration = 100,
  onLongPress,
  delayLongPress,
}) => {
  const { colors } = useTheme();
  const scale = useSharedValue(1);

  const animatedStyles = useAnimatedStyle(() => {
    return {
      transform: [
        {
          scale: scale.value,
        },
      ],
    };
  });

  const handlePressIn = () => {
    scale.value = withSpring(
      pressedScale,
      {
        stiffness: springStiffness,
        damping: springDamping,
      },
      (isFinished) => {
        // Optional: callback after pressIn animation finishes
      }
    );
  };

  const handlePressOut = () => {
    scale.value = withTiming(1, { duration: releaseDuration });
  };

  return (
    <Pressable
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      onLongPress={onLongPress}
      delayLongPress={delayLongPress}
    >
      <Animated.View
        style={[
          styles.container,
          {
            backgroundColor: colors[bg],
            borderRadius: br,
            height: h, // Apply height if provided
            width: w, // Apply width if provided
          },
          animatedStyles,
        ]}
      >
        {children}
      </Animated.View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: "center",
    alignItems: "center",
    display: "flex",
  },
});