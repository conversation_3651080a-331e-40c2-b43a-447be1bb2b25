import { useTheme } from '@context';
import React, { forwardRef } from 'react';
import { ViewProps as RNViewProps, SafeAreaView, View, ViewStyle } from 'react-native';
import { moderateScale } from 'react-native-size-matters';


interface ViewProps extends RNViewProps {
  px?: number;
  py?: number;
}

/**
 * ScreenWrapper is a component that provides a consistent background color
 * and layout for screens in the application. It uses the theme context to
 * apply the background color dynamically.
 *
 * @param {ViewProps} props - The properties passed to the View component.
 * @param {React.Ref<View>} ref - A ref to the View component.
 * @returns {JSX.Element} A styled View component.
 */
export const ScreenWrapper = forwardRef<View, ViewProps>((props, ref) => {

  const { colors } = useTheme();

  const defaultStyles = {
    flex: 1,
    backgroundColor: colors.background,
    ...(props.px != undefined && { paddingHorizontal: moderateScale(props.px, 0.5) }),
    ...(props.py != undefined && { paddingVertical: moderateScale(props.py, 0.5) }),
  } as ViewStyle;

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background }}>
      <View
        ref={ref}
        style={[defaultStyles, props.style]}
        {...props}
      >
        {props.children}
      </View>
    </SafeAreaView>
  );
});
