import { TextInput } from '@components/native';
import { ScreenWrapper } from '@components/shared';
import React, { useState } from 'react';

const ChatTabScreen = () => {
  const [question, setQuestion] = useState('');
  const [name, setName] = useState('');

  return (
    <ScreenWrapper px={10} py={20}>
      <TextInput label='Email address' keyboardType='email-address' value={question} onSuffixIconPress={()=>setQuestion('')}  onChangeText={setQuestion} />
      <TextInput label='Full name' keyboardType='email-address' value={name} onSuffixIconPress={()=>setName('')}  onChangeText={setName} />
    </ScreenWrapper>
  )
}

export default ChatTabScreen