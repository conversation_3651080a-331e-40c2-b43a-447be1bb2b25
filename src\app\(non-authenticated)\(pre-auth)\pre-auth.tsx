import AppLogo from '@assets/svgs/app-logo.svg';
import CurveThread from '@assets/svgs/pre-auth-curve-thread.svg';
import TopBgCurve from '@assets/svgs/pre-auth-top-bg-curve.svg';
import {Button, Text, View} from '@components/native';
import {router} from 'expo-router';
import React from 'react';
import {useWindowDimensions} from 'react-native';

const PreAuthScreen = () => {
    const {height, width} = useWindowDimensions();
    return (
        <View flex={1}>
            <View
                pos="absolute"
                top={-40}
            >
                <TopBgCurve
                    height={height * 0.65}
                    style={{width: width}}
                />
                <View top={-20}>
                    <CurveThread />
                </View>
            </View>
            <View flex={1}>
                <View
                    flexCenterColumn
                    pt={120}
                    gap={20}
                >
                    <AppLogo
                        width={80}
                        height={90}
                    />
                    <Text
                        fw="500"
                        color="neutral80"
                        ff="PlayfairMedium"
                        fs="26"
                    >
                        Welcome to Stitch
                    </Text>
                </View>
            </View>
            <View
                pos="absolute"
                bottom={40}
                flexCenterColumn
                w={width}
                disableSizeMatter
                gap={20}
                px={20}
            >
                <Text
                    color="neutral80"
                    fw="500"
                    fs="24"
                >
                    Heal, Connect, Track!
                </Text>
                <Text
                    ta="center"
                    fs="16"
                >
                    Heal, Connect, Track! Heal, Connect, Track!Heal, Connect, Track!
                </Text>
                <View h={5} />
                <Button
                    fw="500"
                    onPress={() => router.replace('/signup')}
                >
                    SIGN UP
                </Button>
                <View
                    flexCenterRow
                    gap={3}
                    pt={10}
                >
                    <Text
                        fs="12"
                        color="neutral70"
                    >
                        Already have an account?
                    </Text>
                    <Text
                        onPress={() => router.replace('/login')}
                        fs="12"
                        style={{color: '#8E97FD'}}
                    >
                        LOG IN
                    </Text>
                </View>
            </View>
        </View>
    );
};

export default PreAuthScreen;
