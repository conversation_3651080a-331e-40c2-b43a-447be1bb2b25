import ChatIcon from '@assets/svgs/chat-icon.svg';
import FeedIcon from '@assets/svgs/feed-icon.svg';
import JournalIcon from '@assets/svgs/journal-icon.svg';
import MoodIcon from '@assets/svgs/mood-icon.svg';
import NavCurve from "@assets/svgs/nav-curve.svg";
import ProfileIcon from '@assets/svgs/profile-icon.svg';
import { Text, View } from '@components/native';
import { BounceTap } from '@components/shared/animated';
import { useTheme } from '@context';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { DrawerActions } from '@react-navigation/native';
import { ThemeColors } from '@types';
import Constants from 'expo-constants';
import { Tabs, useNavigation } from 'expo-router';
import React, { useReducer } from 'react';
import { LayoutChangeEvent, Pressable, StyleSheet } from 'react-native';
import Animated, { useAnimatedStyle, useDerivedValue, withTiming } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';


const useTabLayout = (routeCount: number, activeIndex: number) => {
  const reducer = (state: Array<{ x: number; index: number }>, action: { x: number; index: number }) => {
    return [...state, { x: action.x, index: action.index }];
  };
  const [layout, dispatch] = useReducer(reducer, []);

  const handleLayout = (event: LayoutChangeEvent, index: number) => {
    dispatch({ x: event.nativeEvent.layout.x, index });
  };

  const xOffset = useDerivedValue(() => {
    if (layout.length !== routeCount) return 0;
    const activeLayout = layout.find(({ index }) => index === activeIndex);
    return activeLayout ? activeLayout.x - 25 : 0;
  }, [activeIndex, layout]);

  return { handleLayout, xOffset };
};

const getTabIcon = (name: string, active: boolean, colors: ThemeColors, isDarkMode: boolean) => {
  const props = { width: 24, height: 24 };
  const fill = active ? (isDarkMode ? colors.purple200 : colors.purple800) : (isDarkMode ? colors.warning30 : colors.purple500);

  switch (name) {
    case 'index': return <ChatIcon {...props} fill={fill} />;
    case 'feed': return <FeedIcon {...props} fill={fill} />;
    case 'journal': return <JournalIcon {...props} fill={fill} />;
    case 'mood': return <MoodIcon {...props} fill2={active ? 'white' : 'gray'} fill={fill} />;
    case 'profile': return <ProfileIcon {...props} fill={fill} />;
    default: return null;
  }
};

const TabBarComponent = ({ active, icon, title, onLayout, onPress }: {
  active: boolean;
  icon: React.ReactNode;
  title: string;
  onLayout: (e: LayoutChangeEvent) => void;
  onPress: () => void;
}) => {
  const { colors, isDarkMode } = useTheme();

  const animatedCircleStyles = useAnimatedStyle(() => ({
    transform: [
      { scale: withTiming(active ? 0.9 : 0, { duration: 250 }) },
      { translateY: withTiming(active ? -10 : 0, { duration: 250 }) },
      { translateX: withTiming(active ? -5 : 0, { duration: 250 }) },
    ],
  }));

  return (
    <Pressable onPress={onPress} onLayout={onLayout} style={styles.component}>
      <Animated.View style={[
        styles.componentCircle,
        animatedCircleStyles,
        { backgroundColor: isDarkMode ? colors.neutral80 : colors.orange, shadowColor: colors.foreground }
      ]}>
        {icon}
      </Animated.View>
      <View style={styles.iconContainer}>
        <View style={{ opacity: active ? 0 : 1 }}>{icon}</View>
        <View style={{ height: 4 }} />
        <View style={styles.tabLabelContainer}>
          <Animated.Text style={[
            styles.tabLabel,
            { color: colors.neutral90, fontWeight: active ? '500' : '400' }
          ]}>
            {title}
          </Animated.Text>
        </View>
      </View>
    </Pressable>
  );
};

const AnimatedTabBar = ({ state, navigation }: any) => {
  const { colors, theme, isDarkMode } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { index: activeIndex, routes } = state;
  const { handleLayout, xOffset } = useTabLayout(routes.length, activeIndex);

  const animatedStyles = useAnimatedStyle(() => ({
    transform: [{ translateX: withTiming(xOffset.value, { duration: 250 }) }],
  }));

  return (
    <View style={[
      {
        paddingBottom: bottom,
        borderTopColor: colors.orange,
        borderTopWidth: 2,
        backgroundColor: theme === 'dark' ? '#081012' : 'white',
      }
    ]}>
      <View style={{ position: "relative", top: -1 }}>
        <View style={{ width: 110, height: 60, position: "absolute" }}>
          <Animated.View style={[styles.activeBackground, animatedStyles]}>
            <NavCurve fill={colors.orange} />
            <View style={{ position: "absolute", top: -2, transform: [{ translateX: 1 }] }}>
              <NavCurve fill={colors.background} width={98} height={43} />
            </View>
          </Animated.View>
        </View>
      </View>

      <View style={styles.tabBarContainer}>
        {routes.map((route: any, index: number) => {
          const active = index === activeIndex;
          const icon = getTabIcon(route.name, active, colors, isDarkMode);
          const title = route.name.charAt(0).toUpperCase() + route.name.slice(1);
          return (
            <TabBarComponent
              key={route.key}
              active={active}
              icon={icon}
              title={title}
              onLayout={(e) => handleLayout(e, index)}
              onPress={() => navigation.navigate(route.name)}
            />
          );
        })}
      </View>
    </View>
  );
};

export default function TabLayout() {
  const { colors } = useTheme();

  return (
    <Tabs
      tabBar={(props) => <AnimatedTabBar {...props} />}
      screenOptions={{
        tabBarActiveTintColor: '#604AE6',
        headerShown: true,
        headerTitle: "",
        headerStyle: {
          height: Constants.statusBarHeight + 65,
          backgroundColor: colors.background,
          shadowColor: colors.foreground,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.2,
          shadowRadius: 4,
          elevation: 3,
        },
        headerLeft: () => <AvatarCirleWithMenu />
      }}
    >
      <Tabs.Screen name="index" options={{ title: 'Chat' }} />
      <Tabs.Screen name="feed" options={{ title: 'Feed' }} />
      <Tabs.Screen name="journal" options={{ title: 'Journal' }} />
      <Tabs.Screen name="mood" options={{ title: 'Mood' }} />
      <Tabs.Screen name="profile" options={{ title: 'Profile' }} />
    </Tabs>
  );
}

const AvatarCirleWithMenu = () => {
  const { colors } = useTheme();
  const navigation = useNavigation();


  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  }

  return (
    <BounceTap onPress={openDrawer}>
      <View h={40} w={200} disableSizeMatter display='flex' fd='row' ai='center' gap={22} pl={10}>
        <View pos='relative' display='flex' fd="row">
          <View h={40} w={40} disableSizeMatter bg='positive40' br={120} flexCenterRow>
            <Text fw='600'>A</Text>
          </View>
          <View bw={1} br={100} pos='absolute' right={-10} bg='neutral00' bottom={2} disableSizeMatter bc='neutral20' h={20} w={20} flexCenterRow>
            <MaterialCommunityIcons color={colors.neutral90} name='menu' />
          </View>
        </View>
        <Text fw='700' fs='20'>Hey Lucas</Text>
      </View>
    </BounceTap>
  )
}

const styles = StyleSheet.create({

  activeBackground: {
    position: 'absolute',
    top: -5,
  },
  tabBarContainer: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
  },
  component: {
    height: 60,
    width: 60,
    alignItems: 'center',
  },
  componentCircle: {
    height: 45,
    width: 45,
    borderRadius: 99,
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    top: -10,
    zIndex: -1,
    alignSelf: 'center',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  tabLabelContainer: {
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  tabLabel: {
    fontSize: 12,
  },
});
