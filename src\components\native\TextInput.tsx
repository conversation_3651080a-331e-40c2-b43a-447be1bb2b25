import { useTheme } from '@context';
import { EvilIcons, Feather } from '@expo/vector-icons';
import React from 'react';
import {
    NativeSyntheticEvent,
    Pressable,
    TextInput as RNTextInput,
    TextInputProps as RNTextInputProps,
    StyleSheet,
    TextInputFocusEventData,
    View,
} from 'react-native';

import Animated, {
    useAnimatedStyle,
    useSharedValue,
    withTiming,
} from 'react-native-reanimated';

export interface FloatingTextInputProps extends RNTextInputProps {
    label: string;
    gapBottom?: number;
    suffixIcon?: React.ReactNode; // NEW: Accept suffix icon
    onSuffixIconPress?: () => void;
    isPassword?: boolean;
}

export const TextInput = React.forwardRef<RNTextInput, FloatingTextInputProps>(
    ({ label, value, onFocus, onBlur, suffixIcon, isPassword, gapBottom = 12, style, ...props }, ref) => {
        const { colors } = useTheme();
        const isFocused = useSharedValue(!!value);
        const [isPasswordVisible, setPasswordVisible] = React.useState(false);

        const handleFocus = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
            isFocused.value = true;
            onFocus?.(e);
        };

        const handleBlur = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
            isFocused.value = false;
            onBlur?.(e);
        };

        const animatedContainerStyle = useAnimatedStyle(() => ({
            borderColor: isFocused.value ? colors.neutral20 : 'transparent',
        }));

        const animatedLabelStyle = useAnimatedStyle(() => {
            const shouldFloat = isFocused.value || !!value;
            return {
                top: withTiming(shouldFloat ? 8 : 20, { duration: 200 }),
                fontSize: withTiming(shouldFloat ? 12 : 14, { duration: 200 }),
            };
        });


        const animatedIconStyle = useAnimatedStyle(() => ({
            display: isFocused.get() ? "flex" : "none",
        }))

        const renderSuffixIcon = () => {
            if (suffixIcon) {
                return suffixIcon;
            }

            if (isPassword) {
                return (
                    <Pressable onPress={() => setPasswordVisible(prev => !prev)}>
                        <Feather
                            name={isPasswordVisible ? 'eye' : 'eye-off'}
                            color="#fff"
                            size={20}
                        />
                    </Pressable>
                );
            }

            return value ? (
                <Pressable onPress={props.onSuffixIconPress}>
                    <EvilIcons color={colors.neutral50} size={30} name="close" />
                </Pressable>
            ) : <></>;
        };


        return (
            <>
                <Animated.View style={[styles.container, animatedContainerStyle, { backgroundColor: colors.neutral10 }]}>
                    <Animated.Text style={[styles.label, animatedLabelStyle, { color: colors.neutral70 }]}>
                        {label}
                    </Animated.Text>
                    <View style={styles.inputWrapper}>
                        <RNTextInput
                            ref={ref}
                            value={value}
                            secureTextEntry={isPassword && !isPasswordVisible}
                            style={[
                                styles.input,
                                {
                                    paddingTop: 26,
                                    flex: 1,
                                    color:colors.neutral80,
                                },
                                style,
                            ]}
                            onFocus={handleFocus}
                            onBlur={handleBlur}
                            selectionColor={colors.purple700}
                            placeholder=""
                            {...props}
                        />
                        <Animated.View style={[animatedIconStyle, styles.suffixIcon]}>
                            {renderSuffixIcon()}
                        </Animated.View>
                    </View>
                </Animated.View>
                {gapBottom ? <View style={{ height: gapBottom }} /> : null}
            </>
        );
    }
);

TextInput.displayName = 'TextInput';

const styles = StyleSheet.create({
    container: {
        borderRadius: 16,
        paddingHorizontal: 12,
        paddingTop: 0,
        paddingBottom: 10,
        position: 'relative',
        borderWidth: 1,
    },
    inputWrapper: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    input: {
        fontSize: 16,
        padding: 0,
        paddingLeft: 5,
        margin: 0,
    },
    label: {
        position: 'absolute',
        left: 12,
        paddingHorizontal: 4,
    },
    suffixIcon: {
        marginLeft: 8,
        height: "100%",
        paddingTop: 16,
    },
});
