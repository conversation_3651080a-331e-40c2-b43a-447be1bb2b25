// packages/useStitch/index.ts
import {
  useMutation,
  UseMutationOptions,
  useQuery,
  UseQueryOptions,
} from '@tanstack/react-query';
import { useMemo, useState } from 'react';
import { apiConfig, ApiConfigMap, EndpointKey } from './apis/index';
import { apiClient } from './axios/axios-client';
import { PaginatedApiConfig, PaginatedResponse, Pagination } from './types/common-api-types';

interface QueryResult<T> {
  data: T | undefined;
  refetch: () => Promise<any>;
  isLoading: boolean;
  isError: boolean;
  isSuccess: boolean;
  status: 'idle' | 'error' | 'loading' | 'success';
}

interface PaginatedQueryResult<T> extends QueryResult<T> {
  isInitialLoading: boolean;
  isPaginatedLoading: boolean;
  isInitialError: boolean;
  pagination: Pagination | undefined;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  fetchNextPage: () => void;
}

interface MutationResult<T, V> {
  data: T | undefined;
  isSuccess: boolean;
  isError: boolean;
  mutate: (variables: V) => void;
  mutateAsync: (variables: V) => Promise<T>;
  isMutating: boolean;
  status: 'idle' | 'error' | 'loading' | 'success';
}

function useStitch<K extends EndpointKey>(
  queryKey: K,
  options?: {
    params?: any;
    page?: number;
    queryOptions?: UseQueryOptions<ApiConfigMap[K]['responseType']>;
    mutationOptions?: UseMutationOptions<
      ApiConfigMap[K]['responseType'],
      unknown,
      ApiConfigMap[K] extends { mutationBody: infer B } ? B : never
    >;
  }
): ApiConfigMap[K]['type'] extends 'mutation'
  ? MutationResult<ApiConfigMap[K]['responseType'], ApiConfigMap[K] extends { mutationBody: infer B } ? B : never>
  : ApiConfigMap[K]['type'] extends 'paginated'
  ? PaginatedQueryResult<ApiConfigMap[K]['responseType']>
  : QueryResult<ApiConfigMap[K]['responseType']> {

  const config = apiConfig[queryKey];
  const [currentPage, setCurrentPage] = useState(options?.page || 1);
  const params = options?.params || {};

  const getPath = () =>
  (typeof config.path === 'function'
    ? (config.path as (params: any) => string)(params)
    : config.path);

  const getQueryParams = () => {
    const obj: Record<string, string> = {};
    if (config.type === 'paginated') {
      const paginatedConfig = config as PaginatedApiConfig<any>;
      obj.page = currentPage.toString();
      obj.limit = paginatedConfig.pageSize.toString();
      if (paginatedConfig.queryParamBuilder) {
        Object.assign(obj, paginatedConfig.queryParamBuilder(params));
      }
    }
    return new URLSearchParams(obj).toString();
  };

  const getUrl = () => {
    const path = getPath();
    const queryString = getQueryParams();
    return queryString ? `${path}?${queryString}` : path;
  };

  const queryFn = async (): Promise<ApiConfigMap[K]['responseType']> => {
    const response = await apiClient({
      url: getUrl(),
      method: config.method,
    });
    const data = response.data?.data;
    if (config.type === 'paginated') {
      return {
        ...data,
        hasNextPage: data.pagination?.page < data.pagination?.totalPages,
        hasPreviousPage: data?.pagination?.page > 1,
      } as PaginatedResponse<any>;
    }
    return data as ApiConfigMap[K]['responseType'];
  };

  const mutationFn = async (
    body: ApiConfigMap[K] extends { mutationBody: infer B } ? B : never
  ): Promise<ApiConfigMap[K]['responseType']> => {
    const response = await apiClient({
      url: getUrl(),
      method: config.method,
      data: body,
    });
    return response.data as ApiConfigMap[K]['responseType'];
  };

  const queryResult = useQuery<ApiConfigMap[K]['responseType']>({
    queryKey: [config.baseCacheKey, currentPage, params],
    queryFn,
    staleTime: config.staleTime,
    enabled: config.type !== 'mutation',
    ...options?.queryOptions,
  });

  const mutationResult = useMutation<
    ApiConfigMap[K]['responseType'],
    unknown,
    ApiConfigMap[K] extends { mutationBody: infer B } ? B : never
  >({
    mutationFn: mutationFn,
    ...options?.mutationOptions,
  });

  return useMemo(() => {
    if (config.type === 'mutation') {
      return {
        data: mutationResult.data,
        isSuccess: mutationResult.isSuccess,
        isError: mutationResult.isError,
        mutate: mutationResult.mutate,
        mutateAsync: mutationResult.mutateAsync,
        isMutating: mutationResult.isPending,
        status: mutationResult.status,
      } as MutationResult<
        ApiConfigMap[K]['responseType'],
        ApiConfigMap[K] extends { mutationBody: infer B } ? B : never
      > as any;
    }

    if (config.type === 'paginated') {
      const paginatedData = queryResult.data as PaginatedResponse<any> | undefined;
      return {
        data: queryResult.data,
        refetch: queryResult.refetch,
        isInitialLoading: queryResult.isLoading,
        isPaginatedLoading: queryResult.isFetching,
        isInitialError: queryResult.isError,
        status: queryResult.status,
        pagination: paginatedData?.pagination,
        hasNextPage: paginatedData?.hasNextPage ?? false,
        hasPreviousPage: paginatedData?.hasPreviousPage ?? false,
        fetchNextPage: () => {
          if (paginatedData?.hasNextPage) {
            setCurrentPage((prev) => prev + 1);
            queryResult.refetch();
          }
        },
      } as PaginatedQueryResult<ApiConfigMap[K]['responseType']>;
    }

    return {
      data: queryResult.data,
      refetch: queryResult.refetch,
      isLoading: queryResult.isLoading,
      isError: queryResult.isError,
      isSuccess: queryResult.isSuccess,
      status: queryResult.status,
    } as QueryResult<ApiConfigMap[K]['responseType']>;
  }, [queryResult, mutationResult, config, currentPage, params]);
}

export default useStitch;
