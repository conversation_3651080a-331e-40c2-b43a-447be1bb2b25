import { ENV } from "@config";
import { ExpoSecureStoreKeys } from "@constants";
import axios from "axios";
import * as SecureStore from 'expo-secure-store';
import { apiConfig } from "../apis";

const BASE_URL = ENV.BACKEND_URL?.toString() ?? "";
const ACCESS_TOKEN : string | null = null;

// Function to get the current refresh token from SecureStore
const getRefreshToken = async (): Promise<string | null> => {
  try {
    return await SecureStore.getItemAsync(ExpoSecureStoreKeys.REFRESH_TOKEN);
  } catch (error) {
    console.error("Failed to retrieve refresh token", error);
    return null;
  }
};

// Function to refresh the access token
const refreshAccessToken = async (): Promise<string> => {
  const refreshToken = await getRefreshToken();
  if (!refreshToken) {
    throw new Error("No refresh token available");
  }

  try {
    const response = await axios.post(`${BASE_URL}/auth/refresh`, {
      refreshToken, // Include the refresh token in the request
    });
    return response.data.accessToken; // Adjust based on your API response
  } catch (error) {
    console.error("Failed to refresh access token", error);
    throw error; // Rethrow the error to handle it in the interceptor
  }
};

export const apiClient = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add the Authorization header
apiClient.interceptors.request.use(async (config) => {
  const url = config.url?.replace(BASE_URL, '')?.split('?')[0] || '';
  const match = Object.values(apiConfig).find((c) =>
    typeof c.path === 'string' ? c.path === url : false
  );

  if (match?.protected) {
    // Access token should be stored in memory
    const accessToken = config.headers.Authorization?.toString()?.split(' ')[1]; // Get the access token from headers
    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
  }
  return config;
});

// Response interceptor to handle 401 errors and refresh the token
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      try {
        const newAccessToken = await refreshAccessToken();
        // Store the new access token in memory (you can use a state management solution)
        originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
        return apiClient(originalRequest);
      } catch (refreshError) {
        // Handle token refresh error (e.g., redirect to login)
        console.error("Token refresh failed", refreshError);
        return Promise.reject(refreshError);
      }
    }
    return Promise.reject(error);
  }
);

