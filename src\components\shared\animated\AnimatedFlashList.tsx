import { Text, View } from '@components/native';
import { useTheme } from '@context';
import {
    FlashListP<PERSON>,
    AnimatedFlashList as ShopifyAnimatedFlashList,
} from '@shopify/flash-list';
import { ThemeColorKeys } from '@types';
import { useRef } from 'react';
import { FlatList, NativeScrollEvent, NativeSyntheticEvent, Platform, SafeAreaView, StatusBar, StyleSheet } from 'react-native';

import Animated, {
    Easing,
    SharedValue,
    useAnimatedProps,
    useAnimatedStyle,
    useDerivedValue,
    useSharedValue,
    withTiming,
} from 'react-native-reanimated';
import { Circle } from 'react-native-svg';

const AnimatedSafeArea = Animated.createAnimatedComponent(SafeAreaView);
const AnimatedCircle = Animated.createAnimatedComponent(Circle);

interface CustomProgressRingProps {
    progress: SharedValue<number>; // 0 to 100
    radius?: number;
    strokeWidth?: number;
    color?: string;
    bgColor?: string;
}

export const CustomProgressRing: React.FC<CustomProgressRingProps> = ({
    progress,
    radius = 16,
    strokeWidth = 3,
    color = 'black',
    bgColor = 'lightgray',
}) => {
    const diameter = radius * 2;
    const c = 2 * Math.PI * radius;

    const animatedProps = useAnimatedProps(() => {
        const clampedProgress = Math.min(Math.max(progress.value, 0), 100);
        return {
            strokeDashoffset: c - (c * clampedProgress) / 100,
        };
    });

    return (
        <View display='flex' fd='row' pt={10} jc='center' style={{ backgroundColor: "red", width: "100%", height: 100 }}>
            <Text >Pull to refresh</Text>
            {/* <Svg width={diameter + strokeWidth} height={diameter + strokeWidth}>
                <AnimatedCircle
                    cx={(diameter + strokeWidth) / 2}
                    cy={(diameter + strokeWidth) / 2}
                    r={radius}
                    stroke={bgColor}
                    strokeWidth={strokeWidth}
                    fill="none"
                />
                <AnimatedCircle
                    cx={(diameter + strokeWidth) / 2}
                    cy={(diameter + strokeWidth) / 2}
                    r={radius}
                    stroke={color}
                    strokeWidth={strokeWidth}
                    strokeDasharray={`${c}, ${c}`}
                    animatedProps={animatedProps}
                    strokeLinecap="round"
                    fill="none"
                    rotation="-90"
                    origin={`${(diameter + strokeWidth) / 2}, ${(diameter + strokeWidth) / 2}`}
                />
            </Svg> */}
        </View>
    );
};

export interface AnimatedFlashListProps<T> extends FlashListProps<T> {
    renderItem: any;
    keyExtractor?: any;
    disabledSafeArea?: boolean;
    bg?: ThemeColorKeys;
    onRefresh?: () => Promise<void>;
    isRefreshing?: boolean;
    statusBarBg?: ThemeColorKeys;
}

const RefreshControlBar = ({ pulledDistance, }: { pulledDistance: SharedValue<number> }) => {
    const { colors } = useTheme();

    const animatedStyle = useAnimatedStyle(() => ({
        height: pulledDistance.value,
        backgroundColor: colors.purple500,
    }));

    const progress = useDerivedValue(() => {
        return Math.min(pulledDistance.value * 2, 100);
    });

    return (
        <Animated.View style={[styles.container, animatedStyle]}>
            <CustomProgressRing progress={progress} color="black" bgColor={colors.foreground} />
        </Animated.View>
    );
};

export const AnimatedFlashList: React.FC<AnimatedFlashListProps<any>> = ({
    bg = 'transparent',
    onRefresh,
    isRefreshing = false,
    disabledSafeArea = false,
    statusBarBg,
    ...props
}) => {
    const pulledDistance = useSharedValue(0);
    const isLocked = useSharedValue(false);

    const { colors } = useTheme();
    const flashListRef = useRef<FlatList>(null);

    const onScroll = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
        'worklet';
        if (isRefreshing) return;
        const offsetY = e.nativeEvent.contentOffset.y;
        if (offsetY >= 0) {
            isLocked.value = false;
            pulledDistance.value = withTiming(0, { duration: 300, easing: Easing.elastic() });
            return;
        }

        const maxScrolled = Math.abs(offsetY);

        if (maxScrolled >= 50) {
            isLocked.value = false;
            pulledDistance.value = withTiming(0, { duration: 300, easing: Easing.elastic() });
            flashListRef.current?.scrollToOffset({ offset: pulledDistance.value });
            if (!isRefreshing) {
                onRefresh?.();
            }
            return;
        }
        //  console.log("Here >>");
        //         if (offsetY < 0 && !isLocked.value) {
        //             pulledDistance.value = Math.min(maxScrolled, 100);
        //             if (maxScrolled >= 100) {
        //                 isLocked.value = true;
        //             }
        //         }

        //         if (offsetY >= 0 && !isLocked.value) {
        //             pulledDistance.value = 0;
        //         }
    };

    const BaseComponent = () => (
        <ShopifyAnimatedFlashList
            ref={flashListRef as any}
            // onScroll={onScroll}
            scrollEventThrottle={16}
            bounces
            alwaysBounceVertical
            // refreshControl={<RefreshControlBar pulledDistance={pulledDistance} />}
            contentContainerStyle={{ backgroundColor: colors[bg] }}
            {...props}
        />
    );

    if (disabledSafeArea) {
        return <BaseComponent />;
    }

    return (
        Platform.OS === "ios" ? (
            <AnimatedSafeArea style={{ flex: 1, backgroundColor: statusBarBg ? colors[statusBarBg] : colors.background }}><BaseComponent /></AnimatedSafeArea>
        ) : <View style={{ paddingTop: StatusBar.currentHeight, flex: 1 }}>
            <BaseComponent />
        </View>
    );
};

AnimatedFlashList.displayName = 'AnimatedFlashList';


const styles = StyleSheet.create({
    container: {
        justifyContent: 'center',
        alignItems: 'center',
    },
})